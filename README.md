# 环境监测数据自动化分析插件

## 概述

本插件是一个专为环境监测数据管理系统设计的Chrome浏览器扩展程序，能够自动化查询和分析环境监测历史数据，检测数据异常并生成分析报告。

## 主要功能

### 🔍 自动化查询
- 自动填充查询表单（排口类型、所属地区、企业名称、排口名称等）
- 批量查询不同条件组合的监测数据
- 支持自定义时间范围查询
- 智能等待数据加载完成

### 📊 数据分析
- 基于国家标准和地方标准的异常检测
- 支持多种监测项目：pH值、COD、氨氮、总磷、总氮等
- 实时标记异常数据
- 趋势分析和统计汇总

### 📋 报告导出
- 支持多种格式：Excel、CSV、JSON、HTML
- 包含异常详情、统计分析和建议措施
- 自动生成可视化图表
- 批量导出功能

### 🎛️ 用户界面
- 直观的弹出窗口控制面板
- 实时进度显示和状态更新
- 浮动分析控制面板
- 响应式设计，适配不同屏幕

## 安装步骤

### 方法一：开发者模式安装（推荐）

1. **下载插件文件**
   - 将所有插件文件下载到本地文件夹

2. **打开Chrome扩展程序页面**
   - 在Chrome浏览器中输入：`chrome://extensions/`
   - 或者点击菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在扩展程序页面右上角，打开"开发者模式"开关

4. **加载插件**
   - 点击"加载已解压的扩展程序"
   - 选择包含插件文件的文件夹
   - 点击"选择文件夹"

5. **验证安装**
   - 插件图标应该出现在Chrome工具栏中
   - 点击图标可以打开控制面板

### 方法二：打包安装

1. **打包插件**
   - 在扩展程序页面，点击"打包扩展程序"
   - 选择插件文件夹，生成.crx文件

2. **安装.crx文件**
   - 将.crx文件拖拽到扩展程序页面
   - 确认安装

## 使用指南

### 基本使用流程

1. **打开监测数据页面**
   ```
   访问环境监测系统的历史数据查询页面
   URL格式：*/Web6/MonitorControl/Enterprise/HistoryData.aspx*
   ```

2. **配置分析参数**
   - 点击插件图标打开控制面板
   - 设置时间范围（1小时、6小时、24小时、7天或自定义）
   - 选择分析规则（标准阈值或严格阈值）
   - 配置导出格式

3. **开始自动分析**
   - 点击"开始分析"按钮
   - 插件将自动：
     - 遍历所有排口类型和地区组合
     - 查询每个企业的监测数据
     - 实时分析数据异常
     - 显示进度和统计信息

4. **查看分析结果**
   - 实时查看异常数据统计
   - 页面上异常数据会被高亮标记
   - 点击"查看结果"导出详细报告

### 高级功能

#### 自定义分析规则
```json
{
  "ph": { "min": 6.0, "max": 9.0 },
  "cod": { "max": 100 },
  "nh3n": { "max": 15 }
}
```

#### 批量导出设置
- 支持同时导出多种格式
- 可设置单文件最大记录数
- 自动按时间戳命名文件

#### 趋势分析
- 检测数据突变（变化率>50%）
- 识别上升/下降/波动趋势
- 计算统计指标（均值、方差等）

## 配置说明

### 查询配置
- **时间范围**：支持预设时间段或自定义时间范围
- **自动批量查询**：启用后自动遍历所有条件组合
- **查询延迟**：设置查询间隔，避免请求过于频繁

### 分析规则
- **标准阈值**：基于国家排放标准
- **严格阈值**：基于地方严格标准
- **自定义阈值**：用户自定义限值

### 导出设置
- **导出格式**：Excel、CSV、JSON、HTML
- **包含图表**：在报告中包含可视化图表
- **包含建议**：自动生成整改建议

## 故障排除

### 常见问题

**Q: 插件无法识别页面元素**
A: 检查页面URL是否匹配，确保在正确的监测数据页面使用插件

**Q: 查询过程中断**
A: 可能是网络问题或页面响应慢，可以调整查询延迟时间

**Q: 导出文件为空**
A: 确保有异常数据被检测到，检查分析规则设置是否正确

**Q: 分析进度卡住**
A: 点击"停止分析"后重新开始，或刷新页面重试

### 调试模式

1. **启用调试日志**
   - 在config.json中设置`"enableLogging": true`
   - 打开浏览器开发者工具查看控制台日志

2. **性能监控**
   - 启用`"enablePerformanceMonitoring": true`
   - 监控查询和分析性能

## 技术架构

### 文件结构
```
├── manifest.json          # 插件配置文件
├── popup.html             # 弹出窗口界面
├── popup.css              # 弹出窗口样式
├── popup.js               # 弹出窗口逻辑
├── content.js             # 内容脚本
├── content.css            # 内容脚本样式
├── background.js          # 后台服务
├── analysis-engine.js     # 数据分析引擎
├── export-manager.js      # 导出管理器
├── rules.json             # 分析规则配置
├── config.json            # 插件配置
└── README.md              # 使用文档
```

### 核心模块

1. **Content Script**：页面交互和数据提取
2. **Background Service**：后台任务管理
3. **Analysis Engine**：数据分析和异常检测
4. **Export Manager**：报告生成和文件导出
5. **Popup Controller**：用户界面控制

## 更新日志

### v1.0.0 (2025-07-31)
- 初始版本发布
- 实现基本的自动化查询功能
- 支持标准异常检测规则
- 提供Excel、CSV、JSON导出格式
- 包含完整的用户界面

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 支持与反馈

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- 项目地址：https://github.com/example/monitoring-analysis-plugin

## 贡献指南

欢迎提交Issue和Pull Request来改进本插件。

---

**注意**：本插件仅用于环境监测数据分析，请确保在合规的前提下使用。
