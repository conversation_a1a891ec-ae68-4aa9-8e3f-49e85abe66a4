// 环境监测数据分析插件 - 后台服务（简化版）

// 监听插件安装事件，初始化默认配置
chrome.runtime.onInstalled.addListener(async () => {
    try {
        // 检查是否已有配置
        const result = await chrome.storage.local.get('config');
        if (result.config) {
            console.log('Configuration already exists');
            return;
        }

        // 初始化默认配置
        const defaultConfig = {
            timeRange: '24',
            autoQuery: true,
            enableAnalysis: true,
            thresholdType: 'standard',
            exportFormat: 'excel',
            includeCharts: true
        };

        await chrome.storage.local.set({ config: defaultConfig });
        console.log('Default configuration initialized');
    } catch (error) {
        console.error('Failed to initialize configuration:', error);
    }
});

console.log('Background service initialized');
