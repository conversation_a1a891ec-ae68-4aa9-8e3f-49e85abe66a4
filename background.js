// 环境监测数据分析插件 - 后台服务
class BackgroundService {
    constructor() {
        this.isAnalyzing = false;
        this.analysisResults = [];
        this.currentProgress = 0;
        this.init();
    }

    init() {
        // 监听来自popup和content script的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听插件安装事件
        chrome.runtime.onInstalled.addListener(() => {
            this.initializeStorage();
        });
    }

    async initializeStorage() {
        // 初始化默认配置
        const defaultConfig = {
            timeRange: '24',
            autoQuery: true,
            enableAnalysis: true,
            thresholdType: 'standard',
            exportFormat: 'excel',
            includeCharts: true,
            analysisRules: {
                standard: {
                    ph: { min: 6.0, max: 9.0 },
                    cod: { max: 100 },
                    nh3n: { max: 15 },
                    tp: { max: 3.0 },
                    tn: { max: 20 }
                },
                strict: {
                    ph: { min: 6.5, max: 8.5 },
                    cod: { max: 50 },
                    nh3n: { max: 8 },
                    tp: { max: 1.5 },
                    tn: { max: 15 }
                }
            }
        };

        await chrome.storage.local.set({ config: defaultConfig });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'startAnalysis':
                    await this.startAnalysis(request.config);
                    sendResponse({ success: true });
                    break;

                case 'stopAnalysis':
                    this.stopAnalysis();
                    sendResponse({ success: true });
                    break;

                case 'getProgress':
                    sendResponse({ 
                        progress: this.currentProgress,
                        isAnalyzing: this.isAnalyzing,
                        results: this.analysisResults
                    });
                    break;

                case 'exportResults':
                    await this.exportResults(request.format);
                    sendResponse({ success: true });
                    break;

                case 'dataAnalyzed':
                    this.handleAnalyzedData(request.data);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Background service error:', error);
            sendResponse({ error: error.message });
        }
    }

    async startAnalysis(config) {
        if (this.isAnalyzing) {
            throw new Error('分析已在进行中');
        }

        this.isAnalyzing = true;
        this.currentProgress = 0;
        this.analysisResults = [];

        // 保存配置
        await chrome.storage.local.set({ config });

        // 通知content script开始分析
        const tabs = await chrome.tabs.query({ 
            url: '*://*/Web6/MonitorControl/Enterprise/HistoryData.aspx*' 
        });

        if (tabs.length === 0) {
            throw new Error('请先打开环境监测数据页面');
        }

        // 向所有匹配的标签页发送开始分析消息
        for (const tab of tabs) {
            chrome.tabs.sendMessage(tab.id, {
                action: 'startAnalysis',
                config: config
            });
        }
    }

    stopAnalysis() {
        this.isAnalyzing = false;
        this.currentProgress = 0;

        // 通知所有content script停止分析
        chrome.tabs.query({}, (tabs) => {
            tabs.forEach(tab => {
                chrome.tabs.sendMessage(tab.id, {
                    action: 'stopAnalysis'
                }).catch(() => {
                    // 忽略无法发送消息的标签页
                });
            });
        });
    }

    handleAnalyzedData(data) {
        this.analysisResults.push(data);
        this.updateProgress();
        
        // 通知popup更新进度
        this.notifyPopup({
            type: 'progressUpdate',
            progress: this.currentProgress,
            results: this.analysisResults
        });
    }

    updateProgress() {
        // 根据分析结果更新进度
        // 这里可以根据实际需求调整进度计算逻辑
        this.currentProgress = Math.min(100, this.analysisResults.length * 10);
    }

    async exportResults(format) {
        if (this.analysisResults.length === 0) {
            throw new Error('没有可导出的数据');
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        let filename = `环境监测分析报告_${timestamp}`;
        let content;

        switch (format) {
            case 'excel':
                content = this.generateExcelContent();
                filename += '.xlsx';
                break;
            case 'csv':
                content = this.generateCSVContent();
                filename += '.csv';
                break;
            case 'json':
                content = JSON.stringify(this.analysisResults, null, 2);
                filename += '.json';
                break;
            default:
                throw new Error('不支持的导出格式');
        }

        // 使用Chrome下载API
        const blob = new Blob([content], { 
            type: this.getMimeType(format) 
        });
        const url = URL.createObjectURL(blob);

        await chrome.downloads.download({
            url: url,
            filename: filename,
            saveAs: true
        });
    }

    generateCSVContent() {
        const headers = [
            '企业名称', '排口名称', '排口类型', '所属地区', 
            '监测项目', '监测值', '标准值', '是否异常', 
            '异常类型', '监测时间'
        ];

        let csv = headers.join(',') + '\n';

        this.analysisResults.forEach(result => {
            if (result.anomalies && result.anomalies.length > 0) {
                result.anomalies.forEach(anomaly => {
                    const row = [
                        result.company,
                        result.outlet,
                        result.outletType,
                        result.region,
                        anomaly.parameter,
                        anomaly.value,
                        anomaly.threshold,
                        '是',
                        anomaly.type,
                        result.timestamp
                    ];
                    csv += row.map(field => `"${field}"`).join(',') + '\n';
                });
            }
        });

        return csv;
    }

    generateExcelContent() {
        // 简化的Excel内容生成（实际应用中可能需要使用专门的库）
        return this.generateCSVContent();
    }

    getMimeType(format) {
        const mimeTypes = {
            'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv',
            'json': 'application/json'
        };
        return mimeTypes[format] || 'text/plain';
    }

    notifyPopup(message) {
        // 尝试向popup发送消息
        chrome.runtime.sendMessage(message).catch(() => {
            // popup可能未打开，忽略错误
        });
    }
}

// 初始化后台服务
const backgroundService = new BackgroundService();
