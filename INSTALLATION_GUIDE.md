# 环境监测数据自动化分析插件 - 安装使用指南

## 快速开始

### 第一步：安装插件

1. **下载插件文件**
   - 确保所有文件都在同一个文件夹中
   - 文件清单：
     ```
     ✓ manifest.json
     ✓ popup.html, popup.css, popup.js
     ✓ content.js, content.css
     ✓ background.js
     ✓ analysis-engine.js
     ✓ export-manager.js
     ✓ rules.json, config.json
     ```

2. **在Chrome中安装**
   - 打开Chrome浏览器
   - 地址栏输入：`chrome://extensions/`
   - 开启右上角的"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择插件文件夹
   - 安装成功后，工具栏会出现插件图标 🌱

### 第二步：打开监测数据页面

访问环境监测系统的历史数据查询页面，URL通常包含：
```
*/Web6/MonitorControl/Enterprise/HistoryData.aspx*
```

### 第三步：配置和使用

1. **点击插件图标**打开控制面板
2. **配置查询参数**：
   - 时间范围：选择最近24小时（推荐）
   - 启用自动批量查询
   - 启用数据异常分析
3. **点击"开始分析"**
4. **等待分析完成**，查看实时进度
5. **点击"查看结果"**导出报告

## 详细操作步骤

### 配置说明

#### 查询配置
- **时间范围选项**：
  - 最近1小时：适合实时监控
  - 最近6小时：适合短期分析
  - 最近24小时：推荐日常使用
  - 最近7天：适合周期性分析
  - 自定义：指定具体时间段

#### 分析规则
- **标准阈值**：基于国家排放标准
  - pH值：6.0-9.0
  - COD：≤100mg/L
  - 氨氮：≤15mg/L
  - 总磷：≤3.0mg/L
  
- **严格阈值**：基于地方严格标准
  - pH值：6.5-8.5
  - COD：≤50mg/L
  - 氨氮：≤8mg/L
  - 总磷：≤1.5mg/L

#### 导出格式
- **Excel (.xlsx)**：推荐，包含完整数据和格式
- **CSV (.csv)**：适合数据处理
- **JSON (.json)**：适合程序处理
- **HTML (.html)**：适合打印和查看

### 工作流程示例

#### 场景1：日常监管检查
```
1. 设置时间范围为"最近24小时"
2. 选择"标准阈值"
3. 启用"自动批量查询"
4. 开始分析，等待完成
5. 导出Excel报告
6. 重点关注高严重程度异常
```

#### 场景2：专项检查
```
1. 设置"自定义时间范围"
2. 选择"严格阈值"
3. 针对特定企业或地区
4. 导出详细的HTML报告
5. 制定整改措施
```

#### 场景3：趋势分析
```
1. 设置较长时间范围（7天）
2. 启用趋势分析
3. 关注数据变化趋势
4. 识别潜在问题企业
```

## 界面说明

### 主控制面板
- **查询配置区域**：设置时间范围和查询选项
- **分析规则区域**：选择异常检测标准
- **导出设置区域**：配置报告格式
- **操作按钮区域**：开始/停止/查看结果
- **状态显示区域**：进度条和统计信息

### 页面浮动面板
- **状态信息**：当前分析状态
- **进度显示**：查询进度百分比
- **异常计数**：实时异常数量
- **快捷操作**：开始/停止/导出按钮

### 数据标记
- **红色高亮**：高严重程度异常
- **橙色高亮**：中等严重程度异常
- **灰色标记**：数据缺失或无效

## 报告解读

### 报告结构
1. **概览信息**
   - 分析时间和范围
   - 总记录数和异常数
   - 异常率统计

2. **异常详情表**
   - 企业名称和排口信息
   - 监测项目和数值
   - 异常类型和严重程度
   - 超标倍数

3. **统计分析**
   - 按监测项目统计
   - 按严重程度统计
   - 按企业统计

4. **建议措施**
   - 紧急处理建议
   - 重点监控建议
   - 总体改进建议

### 异常类型说明
- **超过最大值**：监测值超过排放标准上限
- **低于最小值**：监测值低于排放标准下限（如pH值）
- **趋势异常**：数据变化趋势异常
- **数据缺失**：监测数据缺失或无效

### 严重程度分级
- **高**：超标2倍以上，需要立即处理
- **中**：超标但在2倍以内，需要关注
- **低**：轻微异常或数据问题

## 常见问题解答

### Q1: 插件无法正常工作
**可能原因**：
- 页面URL不匹配
- 页面元素结构变化
- 网络连接问题

**解决方法**：
- 确认在正确的监测数据页面
- 刷新页面重试
- 检查网络连接

### Q2: 查询速度很慢
**可能原因**：
- 查询数据量大
- 网络响应慢
- 服务器负载高

**解决方法**：
- 缩短时间范围
- 增加查询延迟
- 分批次查询

### Q3: 导出的报告为空
**可能原因**：
- 没有检测到异常数据
- 分析规则设置过于宽松
- 数据格式不匹配

**解决方法**：
- 检查分析规则设置
- 确认数据格式正确
- 查看控制台错误信息

### Q4: 异常检测不准确
**可能原因**：
- 分析规则不适合
- 数据单位不匹配
- 参数映射错误

**解决方法**：
- 调整分析规则
- 检查数据单位
- 更新参数映射配置

## 性能优化建议

### 提高查询效率
1. **合理设置时间范围**：避免查询过长时间段
2. **调整查询延迟**：根据服务器响应速度调整
3. **分批处理**：大量数据时分批查询
4. **网络优化**：确保网络连接稳定

### 减少资源占用
1. **关闭不必要的标签页**
2. **定期清理浏览器缓存**
3. **避免同时运行多个分析任务**
4. **及时导出和清理结果**

## 高级功能

### 自定义分析规则
可以修改`rules.json`文件来自定义分析规则：
```json
{
  "analysisRules": {
    "custom": {
      "parameters": {
        "cod": { "max": 80 },
        "nh3n": { "max": 12 }
      }
    }
  }
}
```

### 批量导出
支持同时导出多种格式的报告，适合不同用途：
- Excel：日常办公使用
- CSV：数据分析处理
- JSON：程序接口调用
- HTML：打印和展示

### 定时任务
可以结合浏览器的定时任务功能，实现定期自动分析。

## 技术支持

如需技术支持，请提供以下信息：
1. 插件版本号
2. Chrome浏览器版本
3. 操作系统版本
4. 错误截图或日志
5. 具体操作步骤

联系方式：
- 邮箱：<EMAIL>
- 技术文档：查看README.md

---

**提示**：建议在使用前先在测试环境中熟悉插件功能，确保数据安全。
