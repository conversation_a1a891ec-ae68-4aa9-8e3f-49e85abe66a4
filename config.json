{"version": "1.0.0", "name": "环境监测数据自动化分析插件", "description": "自动化查询和分析环境监测历史数据，检测异常并导出报告", "author": "环境监测数据分析团队", "homepage": "", "supportEmail": "", "defaultSettings": {"timeRange": "24", "autoQuery": true, "enableAnalysis": true, "thresholdType": "standard", "exportFormat": "excel", "includeCharts": true, "queryDelay": 1000, "maxRetries": 3, "batchSize": 10}, "pageSelectors": {"outletType": ["#sel_SubType", "select[name*='排口类型']", "select[name*='OutletType']"], "region": ["#sel_City", "select[name*='地区']", "select[name*='Region']"], "company": ["#sel_ent", "select[name*='企业']", "select[name*='Company']"], "outlet": ["#select_sub", "select[name*='排口']", "select[name*='Outlet']"], "parameter": ["#sel_Item", "select[name*='监测项目']", "select[name*='Parameter']"], "timeInputs": {"minute": {"start": "#startMin", "end": "#endMin"}, "hour": {"start": "#startHours", "end": "#endHours"}, "day": {"start": "#startDays", "end": "#endDays"}, "month": {"start": "#startMonth", "end": "#endMonth"}, "year": {"start": "#startYear", "end": "#endYear"}}, "queryButton": ["button[onclick*='HistoryReport.doSearch']", "input[value*='查询']", "button[onclick*='查询']"], "dataTable": ["#dataBasic", "table[id*='data']", "table[class*='data']"], "timeTypeRadios": "input[name='myRadio']", "hideInvalidData": "#chk_ShowInvalidData", "showUploadData": "#chk_UploadData"}, "dataTableColumns": {"timestamp": 0, "parameter": 1, "value": 2, "unit": 3, "status": 4, "company": "从查询条件获取", "outlet": "从查询条件获取", "outletType": "从查询条件获取"}, "exportSettings": {"formats": ["excel", "csv", "json", "pdf"], "defaultFormat": "excel", "includeCharts": true, "includeStatistics": true, "includeRecommendations": true, "maxRecordsPerFile": 10000}, "analysisSettings": {"enableTrendAnalysis": true, "trendWindowSize": 10, "anomalyThreshold": 0.5, "severityLevels": {"low": {"color": "#9e9e9e", "priority": 1}, "medium": {"color": "#ff9800", "priority": 2}, "high": {"color": "#f44336", "priority": 3}}}, "uiSettings": {"theme": "default", "language": "zh-CN", "showNotifications": true, "autoSave": true, "refreshInterval": 1000}, "debugSettings": {"enableLogging": true, "logLevel": "info", "enablePerformanceMonitoring": false}}