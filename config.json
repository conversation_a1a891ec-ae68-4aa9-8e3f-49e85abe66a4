{"version": "1.0.0", "name": "环境监测数据自动化分析插件", "description": "自动化查询和分析环境监测历史数据，检测异常并导出报告", "author": "环境监测数据分析团队", "homepage": "", "supportEmail": "", "defaultSettings": {"timeRange": "24", "autoQuery": true, "enableAnalysis": true, "thresholdType": "standard", "exportFormat": "excel", "includeCharts": true, "queryDelay": 1000, "maxRetries": 3, "batchSize": 10}, "pageSelectors": {"outletType": ["select[name*='排口类型']", "select[name*='OutletType']", "#ddlOutletType", ".outlet-type-select"], "region": ["select[name*='地区']", "select[name*='Region']", "#ddlRegion", ".region-select"], "company": ["select[name*='企业']", "select[name*='Company']", "#ddlCompany", ".company-select"], "outlet": ["select[name*='排口']", "select[name*='Outlet']", "#ddlOutlet", ".outlet-select"], "parameter": ["select[name*='监测项目']", "select[name*='Parameter']", "#ddlParameter", ".parameter-select"], "startTime": ["input[name*='开始时间']", "input[name*='StartTime']", "#txtStartTime", ".start-time-input"], "endTime": ["input[name*='结束时间']", "input[name*='EndTime']", "#txtEndTime", ".end-time-input"], "queryButton": ["input[value*='查询']", "button[onclick*='查询']", "#btnQuery", ".query-button"], "dataTable": ["table[id*='data']", "table[class*='data']", "#gvData", ".data-table"]}, "dataTableColumns": {"timestamp": 0, "company": 1, "outlet": 2, "outletType": 3, "parameter": 4, "value": 5, "unit": 6, "status": 7}, "exportSettings": {"formats": ["excel", "csv", "json", "pdf"], "defaultFormat": "excel", "includeCharts": true, "includeStatistics": true, "includeRecommendations": true, "maxRecordsPerFile": 10000}, "analysisSettings": {"enableTrendAnalysis": true, "trendWindowSize": 10, "anomalyThreshold": 0.5, "severityLevels": {"low": {"color": "#9e9e9e", "priority": 1}, "medium": {"color": "#ff9800", "priority": 2}, "high": {"color": "#f44336", "priority": 3}}}, "uiSettings": {"theme": "default", "language": "zh-CN", "showNotifications": true, "autoSave": true, "refreshInterval": 1000}, "debugSettings": {"enableLogging": true, "logLevel": "info", "enablePerformanceMonitoring": false}}