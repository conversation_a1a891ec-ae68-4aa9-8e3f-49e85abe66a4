<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>插件测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>环境监测数据分析插件 - 测试页面</h1>
        
        <div class="test-section">
            <h3>1. 插件检测</h3>
            <p>检测插件是否正确加载和运行</p>
            <button onclick="testPluginDetection()">检测插件</button>
            <div id="plugin-status" class="status info">等待检测...</div>
        </div>

        <div class="test-section">
            <h3>2. 存储功能测试</h3>
            <p>测试Chrome存储API功能</p>
            <button onclick="testStorage()">测试存储</button>
            <div id="storage-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>3. 消息通信测试</h3>
            <p>测试popup和content script之间的通信</p>
            <button onclick="testMessaging()">测试通信</button>
            <div id="messaging-status" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>4. 页面元素检测</h3>
            <p>检测页面关键元素是否存在（需要在实际监测页面测试）</p>
            <button onclick="testPageElements()">检测元素</button>
            <div id="elements-status" class="status info">等待检测...</div>
        </div>

        <div class="test-section">
            <h3>5. 测试日志</h3>
            <div id="test-log" class="log">测试日志将显示在这里...\n</div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function setStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function testPluginDetection() {
            log('开始检测插件...');
            
            // 检查是否在Chrome扩展环境中
            if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.id) {
                setStatus('plugin-status', 'success', '插件环境检测成功');
                log('Chrome扩展API可用');
                
                // 检查manifest信息
                const manifest = chrome.runtime.getManifest();
                log(`插件名称: ${manifest.name}`);
                log(`插件版本: ${manifest.version}`);
                
            } else {
                setStatus('plugin-status', 'error', '插件环境检测失败 - 请在Chrome扩展中运行');
                log('Chrome扩展API不可用');
            }
        }

        async function testStorage() {
            log('开始测试存储功能...');
            
            try {
                // 测试写入
                const testData = { test: 'value', timestamp: Date.now() };
                await chrome.storage.local.set({ testData });
                log('存储写入成功');
                
                // 测试读取
                const result = await chrome.storage.local.get('testData');
                if (result.testData && result.testData.test === 'value') {
                    setStatus('storage-status', 'success', '存储功能测试成功');
                    log('存储读取成功');
                } else {
                    setStatus('storage-status', 'error', '存储读取失败');
                    log('存储读取失败');
                }
                
                // 清理测试数据
                await chrome.storage.local.remove('testData');
                log('测试数据已清理');
                
            } catch (error) {
                setStatus('storage-status', 'error', '存储功能测试失败: ' + error.message);
                log('存储测试错误: ' + error.message);
            }
        }

        async function testMessaging() {
            log('开始测试消息通信...');
            
            try {
                // 尝试获取当前标签页
                const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
                if (tabs.length === 0) {
                    setStatus('messaging-status', 'error', '无法获取当前标签页');
                    log('无法获取当前标签页');
                    return;
                }
                
                log(`当前标签页URL: ${tabs[0].url}`);
                
                // 检查是否在目标页面
                if (tabs[0].url.includes('HistoryData.aspx')) {
                    log('在目标监测页面，尝试发送消息...');
                    
                    const response = await chrome.tabs.sendMessage(tabs[0].id, {
                        action: 'ping'
                    });
                    
                    if (response) {
                        setStatus('messaging-status', 'success', '消息通信测试成功');
                        log('收到content script响应: ' + JSON.stringify(response));
                    } else {
                        setStatus('messaging-status', 'error', '未收到content script响应');
                        log('未收到content script响应');
                    }
                } else {
                    setStatus('messaging-status', 'info', '请在环境监测数据页面测试通信功能');
                    log('当前不在目标页面，无法测试通信');
                }
                
            } catch (error) {
                setStatus('messaging-status', 'error', '消息通信测试失败: ' + error.message);
                log('通信测试错误: ' + error.message);
            }
        }

        function testPageElements() {
            log('开始检测页面元素...');
            
            const elements = [
                '#sel_SubType',
                '#sel_City', 
                '#sel_ent',
                '#select_sub',
                '#sel_Item',
                '#dataBasic',
                'input[name="myRadio"]'
            ];
            
            let foundCount = 0;
            elements.forEach(selector => {
                const element = document.querySelector(selector);
                if (element) {
                    foundCount++;
                    log(`✓ 找到元素: ${selector}`);
                } else {
                    log(`✗ 未找到元素: ${selector}`);
                }
            });
            
            if (foundCount === elements.length) {
                setStatus('elements-status', 'success', `所有关键元素检测成功 (${foundCount}/${elements.length})`);
            } else if (foundCount > 0) {
                setStatus('elements-status', 'info', `部分元素检测成功 (${foundCount}/${elements.length})`);
            } else {
                setStatus('elements-status', 'error', '未检测到任何关键元素，请在监测数据页面测试');
            }
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '测试日志已清空...\n';
        }

        // 页面加载完成后自动检测插件
        document.addEventListener('DOMContentLoaded', () => {
            log('测试页面加载完成');
            testPluginDetection();
        });
    </script>
</body>
</html>
