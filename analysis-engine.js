// 环境监测数据分析引擎
class AnalysisEngine {
    constructor() {
        this.rules = null;
        this.parameterMapping = null;
        this.anomalyTypes = null;
        this.init();
    }

    async init() {
        try {
            // 加载分析规则
            const response = await fetch(chrome.runtime.getURL('rules.json'));
            const rulesData = await response.json();
            
            this.rules = rulesData.analysisRules;
            this.parameterMapping = rulesData.parameterMapping;
            this.anomalyTypes = rulesData.anomalyTypes;
            
            console.log('分析引擎初始化完成');
        } catch (error) {
            console.error('分析引擎初始化失败:', error);
        }
    }

    /**
     * 分析监测数据，检测异常
     * @param {Array} dataRows - 数据行数组
     * @param {string} ruleType - 规则类型 (standard/strict)
     * @param {Object} queryInfo - 查询信息
     * @returns {Object} 分析结果
     */
    analyzeData(dataRows, ruleType = 'standard', queryInfo = {}) {
        if (!this.rules || !dataRows || dataRows.length === 0) {
            return { anomalies: [], statistics: {} };
        }

        const anomalies = [];
        const statistics = {
            totalRecords: dataRows.length,
            anomalyCount: 0,
            parameterStats: {},
            severityStats: { high: 0, medium: 0, low: 0 }
        };

        const currentRules = this.rules[ruleType];
        if (!currentRules) {
            console.error('未找到指定的分析规则:', ruleType);
            return { anomalies: [], statistics: {} };
        }

        // 分析每行数据
        dataRows.forEach((row, index) => {
            const rowAnomalies = this.analyzeRow(row, currentRules, queryInfo, index);
            anomalies.push(...rowAnomalies);
            
            // 更新统计信息
            rowAnomalies.forEach(anomaly => {
                statistics.anomalyCount++;
                
                // 参数统计
                if (!statistics.parameterStats[anomaly.parameter]) {
                    statistics.parameterStats[anomaly.parameter] = 0;
                }
                statistics.parameterStats[anomaly.parameter]++;
                
                // 严重程度统计
                statistics.severityStats[anomaly.severity]++;
            });
        });

        return {
            anomalies: anomalies,
            statistics: statistics,
            queryInfo: queryInfo,
            analysisTime: new Date().toISOString(),
            ruleType: ruleType
        };
    }

    /**
     * 分析单行数据
     * @param {Array} row - 数据行
     * @param {Object} rules - 分析规则
     * @param {Object} queryInfo - 查询信息
     * @param {number} rowIndex - 行索引
     * @returns {Array} 异常数组
     */
    analyzeRow(row, rules, queryInfo, rowIndex) {
        const anomalies = [];
        
        // 根据实际数据结构调整字段索引
        // 假设数据结构：[时间, 企业名称, 排口名称, 排口类型, 监测项目, 监测值, 单位, ...]
        const timestamp = row[0] || '';
        const company = row[1] || queryInfo.company || '';
        const outlet = row[2] || queryInfo.outlet || '';
        const outletType = row[3] || queryInfo.outletType || '';
        const parameter = row[4] || '';
        const valueStr = row[5] || '';
        const unit = row[6] || '';

        // 解析监测值
        const value = this.parseValue(valueStr);
        if (value === null) {
            // 数据缺失或无效
            anomalies.push({
                rowIndex: rowIndex,
                timestamp: timestamp,
                company: company,
                outlet: outlet,
                outletType: outletType,
                parameter: parameter,
                value: valueStr,
                unit: unit,
                anomalyType: 'missing_data',
                severity: 'low',
                description: '监测数据缺失或无效',
                originalRow: row
            });
            return anomalies;
        }

        // 获取参数对应的规则
        const ruleKey = this.getParameterRuleKey(parameter);
        if (!ruleKey || !rules.parameters[ruleKey]) {
            return anomalies; // 没有对应规则，跳过
        }

        const rule = rules.parameters[ruleKey];
        
        // 检查最大值
        if (rule.max !== undefined && value > rule.max) {
            const severity = value > rule.max * 2 ? 'high' : 'medium';
            anomalies.push({
                rowIndex: rowIndex,
                timestamp: timestamp,
                company: company,
                outlet: outlet,
                outletType: outletType,
                parameter: parameter,
                value: value,
                unit: unit,
                threshold: rule.max,
                thresholdType: 'max',
                anomalyType: 'exceed_max',
                severity: severity,
                description: `${parameter}值${value}${unit}超过最大限值${rule.max}${unit}`,
                exceedRatio: (value / rule.max).toFixed(2),
                originalRow: row
            });
        }

        // 检查最小值
        if (rule.min !== undefined && value < rule.min) {
            anomalies.push({
                rowIndex: rowIndex,
                timestamp: timestamp,
                company: company,
                outlet: outlet,
                outletType: outletType,
                parameter: parameter,
                value: value,
                unit: unit,
                threshold: rule.min,
                thresholdType: 'min',
                anomalyType: 'below_min',
                severity: 'medium',
                description: `${parameter}值${value}${unit}低于最小限值${rule.min}${unit}`,
                originalRow: row
            });
        }

        return anomalies;
    }

    /**
     * 解析监测值
     * @param {string} valueStr - 值字符串
     * @returns {number|null} 解析后的数值
     */
    parseValue(valueStr) {
        if (!valueStr || valueStr.trim() === '' || valueStr === '-' || valueStr === 'N/A') {
            return null;
        }

        // 移除非数字字符（保留小数点和负号）
        const cleanStr = valueStr.toString().replace(/[^\d.-]/g, '');
        const value = parseFloat(cleanStr);
        
        return isNaN(value) ? null : value;
    }

    /**
     * 获取参数对应的规则键
     * @param {string} parameter - 监测项目名称
     * @returns {string|null} 规则键
     */
    getParameterRuleKey(parameter) {
        if (!parameter) return null;
        
        const parameterLower = parameter.toLowerCase().trim();
        
        // 直接匹配
        if (this.parameterMapping[parameterLower]) {
            return this.parameterMapping[parameterLower];
        }

        // 模糊匹配
        for (const [key, value] of Object.entries(this.parameterMapping)) {
            if (parameterLower.includes(key) || key.includes(parameterLower)) {
                return value;
            }
        }

        return null;
    }

    /**
     * 趋势分析
     * @param {Array} timeSeriesData - 时间序列数据
     * @param {string} parameter - 监测项目
     * @returns {Object} 趋势分析结果
     */
    analyzeTrend(timeSeriesData, parameter) {
        if (!timeSeriesData || timeSeriesData.length < 3) {
            return { trend: 'insufficient_data', anomalies: [] };
        }

        const values = timeSeriesData.map(d => d.value).filter(v => v !== null);
        if (values.length < 3) {
            return { trend: 'insufficient_data', anomalies: [] };
        }

        // 计算趋势
        const trend = this.calculateTrend(values);
        const anomalies = [];

        // 检测突变
        for (let i = 1; i < values.length; i++) {
            const current = values[i];
            const previous = values[i - 1];
            const changeRate = Math.abs((current - previous) / previous);

            // 如果变化率超过50%，标记为异常
            if (changeRate > 0.5) {
                anomalies.push({
                    index: i,
                    timestamp: timeSeriesData[i].timestamp,
                    parameter: parameter,
                    value: current,
                    previousValue: previous,
                    changeRate: changeRate,
                    anomalyType: 'trend_abnormal',
                    severity: changeRate > 1.0 ? 'high' : 'medium',
                    description: `${parameter}值突变，变化率${(changeRate * 100).toFixed(1)}%`
                });
            }
        }

        return {
            trend: trend,
            anomalies: anomalies,
            statistics: {
                mean: values.reduce((a, b) => a + b, 0) / values.length,
                min: Math.min(...values),
                max: Math.max(...values),
                variance: this.calculateVariance(values)
            }
        };
    }

    /**
     * 计算趋势
     * @param {Array} values - 数值数组
     * @returns {string} 趋势类型
     */
    calculateTrend(values) {
        if (values.length < 2) return 'stable';

        let increasing = 0;
        let decreasing = 0;

        for (let i = 1; i < values.length; i++) {
            if (values[i] > values[i - 1]) {
                increasing++;
            } else if (values[i] < values[i - 1]) {
                decreasing++;
            }
        }

        const total = values.length - 1;
        const increasingRatio = increasing / total;
        const decreasingRatio = decreasing / total;

        if (increasingRatio > 0.7) return 'increasing';
        if (decreasingRatio > 0.7) return 'decreasing';
        if (increasingRatio > 0.4 && decreasingRatio > 0.4) return 'fluctuating';
        return 'stable';
    }

    /**
     * 计算方差
     * @param {Array} values - 数值数组
     * @returns {number} 方差
     */
    calculateVariance(values) {
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const squaredDiffs = values.map(value => Math.pow(value - mean, 2));
        return squaredDiffs.reduce((a, b) => a + b, 0) / values.length;
    }

    /**
     * 生成分析报告
     * @param {Object} analysisResult - 分析结果
     * @returns {Object} 报告对象
     */
    generateReport(analysisResult) {
        const { anomalies, statistics, queryInfo, analysisTime, ruleType } = analysisResult;

        const report = {
            title: '环境监测数据异常分析报告',
            generatedAt: analysisTime,
            ruleType: ruleType,
            summary: {
                totalRecords: statistics.totalRecords,
                anomalyCount: statistics.anomalyCount,
                anomalyRate: ((statistics.anomalyCount / statistics.totalRecords) * 100).toFixed(2) + '%',
                queryInfo: queryInfo
            },
            anomalies: anomalies,
            statistics: statistics,
            recommendations: this.generateRecommendations(anomalies, statistics)
        };

        return report;
    }

    /**
     * 生成建议措施
     * @param {Array} anomalies - 异常数组
     * @param {Object} statistics - 统计信息
     * @returns {Array} 建议数组
     */
    generateRecommendations(anomalies, statistics) {
        const recommendations = [];

        // 高严重程度异常的建议
        if (statistics.severityStats.high > 0) {
            recommendations.push({
                type: 'urgent',
                title: '紧急处理建议',
                description: `发现${statistics.severityStats.high}项高严重程度异常，建议立即进行现场检查和整改。`
            });
        }

        // 频繁异常参数的建议
        const frequentParams = Object.entries(statistics.parameterStats)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3);

        if (frequentParams.length > 0) {
            recommendations.push({
                type: 'monitoring',
                title: '重点监控建议',
                description: `以下监测项目异常频率较高，建议加强监控：${frequentParams.map(([param]) => param).join('、')}`
            });
        }

        // 总体建议
        if (statistics.anomalyCount > 0) {
            recommendations.push({
                type: 'general',
                title: '总体建议',
                description: '建议企业加强环保设施运行管理，确保污染物稳定达标排放。'
            });
        }

        return recommendations;
    }
}

// 导出分析引擎（用于其他模块）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnalysisEngine;
} else if (typeof window !== 'undefined') {
    window.AnalysisEngine = AnalysisEngine;
}
