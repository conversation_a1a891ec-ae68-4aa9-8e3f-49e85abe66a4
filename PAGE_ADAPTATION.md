# 页面适配说明

## 页面结构分析

根据提供的页面源码，插件已针对以下页面结构进行了优化：

### 页面特征
- **框架**: Bootstrap + EasyUI + jQuery
- **数据表格**: EasyUI DataGrid (`#dataBasic`)
- **选择器**: Bootstrap Select组件
- **时间控制**: 多种时间类型（实时、分钟、小时、日、月、年）

### 关键元素映射

| 功能 | 元素ID | 说明 |
|------|--------|------|
| 排口类型 | `#sel_SubType` | Bootstrap Select下拉框 |
| 所属地区 | `#sel_City` | Bootstrap Select下拉框 |
| 企业名称 | `#sel_ent` | Bootstrap Select下拉框，支持搜索 |
| 排口名称 | `#select_sub` | Bootstrap Select下拉框，支持搜索 |
| 监测项目 | `#sel_Item` | Bootstrap Select多选框 |
| 时间类型 | `input[name="myRadio"]` | 单选按钮组 |
| 查询按钮 | `button[onclick*="HistoryReport.doSearch"]` | 查询触发按钮 |
| 数据表格 | `#dataBasic` | EasyUI DataGrid |

### 时间输入框映射

| 时间类型 | 值 | 开始时间ID | 结束时间ID |
|----------|----|-----------|-----------| 
| 实时 | -1 | `#startMin` | `#endMin` |
| 分钟 | 0 | `#startMin` | `#endMin` |
| 小时 | 1 | `#startHours` | `#endHours` |
| 日 | 2 | `#startDays` | `#endDays` |
| 月 | 3 | `#startMonth` | `#endMonth` |
| 年 | 4 | `#startYear` | `#endYear` |

## 插件适配优化

### 1. 元素检测优化
```javascript
// 针对实际页面元素ID进行精确匹配
outletType: document.querySelector('#sel_SubType'),
region: document.querySelector('#sel_City'),
company: document.querySelector('#sel_ent'),
outlet: document.querySelector('#select_sub'),
parameter: document.querySelector('#sel_Item')
```

### 2. Bootstrap Select组件处理
```javascript
// 使用jQuery API操作Bootstrap Select
$(selectElement).selectpicker('val', value);
$(selectElement).trigger('change');
```

### 3. EasyUI DataGrid数据提取
```javascript
// 使用EasyUI API获取数据
const rows = $(this.elements.dataTable).datagrid('getRows');
```

### 4. 时间控制适配
- 自动检测当前选中的时间类型
- 根据时间类型选择对应的输入框
- 支持多种时间格式

### 5. 数据结构适配
```javascript
// 根据实际数据结构调整字段索引
const timestamp = row[0];    // 时间
const parameter = row[1];    // 监测项目  
const valueStr = row[2];     // 监测值
const unit = row[3];         // 单位
const status = row[4];       // 状态
```

## 工作流程

### 自动化查询流程
1. **初始化检测** - 等待页面关键元素加载完成
2. **生成查询队列** - 遍历所有排口类型和地区组合
3. **批量查询执行**:
   - 设置排口类型 → 等待地区列表更新
   - 设置所属地区 → 等待企业列表更新  
   - 设置企业名称 → 等待排口列表更新
   - 设置排口名称 → 等待监测项目更新
   - 选择所有监测项目
   - 设置时间范围
   - 点击查询按钮
   - 等待数据加载完成
4. **数据分析** - 提取表格数据并进行异常检测
5. **结果标记** - 在页面上高亮显示异常数据

### 异常检测流程
1. **数据提取** - 从EasyUI DataGrid获取行数据
2. **规则匹配** - 根据监测项目匹配分析规则
3. **阈值检查** - 检查是否超过最大值或低于最小值
4. **异常分类** - 按严重程度分类（高/中/低）
5. **页面标记** - 在表格中高亮显示异常行

## 页面兼容性

### 支持的页面版本
- 基于Bootstrap 3.x的页面
- 使用EasyUI DataGrid的数据表格
- 包含HistoryReport对象的页面
- URL匹配`*/Web6/MonitorControl/Enterprise/HistoryData.aspx*`

### 依赖检查
插件会检查以下依赖：
- jQuery库 (`$`)
- EasyUI库 (`$.fn.datagrid`)
- HistoryReport对象 (`window.HistoryReport`)
- Bootstrap Select插件 (`$.fn.selectpicker`)

### 错误处理
- 元素未找到时的降级处理
- 网络请求超时的重试机制
- 数据格式异常的容错处理
- 页面结构变化的适配能力

## 性能优化

### 查询优化
- 智能等待机制，避免过快的连续请求
- 批量处理，减少单次查询的数据量
- 缓存机制，避免重复查询相同条件

### 内存管理
- 及时清理不需要的数据
- 避免内存泄漏
- 优化大数据量的处理

### 用户体验
- 实时进度显示
- 异常数据即时高亮
- 响应式的控制面板
- 清晰的状态提示

## 故障排除

### 常见问题
1. **元素未找到** - 检查页面URL和元素ID是否匹配
2. **查询无响应** - 检查网络连接和服务器状态
3. **数据不显示** - 检查EasyUI DataGrid是否正常加载
4. **异常检测不准确** - 检查分析规则和数据格式

### 调试方法
1. 打开浏览器开发者工具
2. 查看控制台日志输出
3. 检查网络请求状态
4. 验证页面元素是否存在

### 配置调整
可以通过修改`config.json`文件来调整：
- 查询延迟时间
- 最大重试次数
- 批量处理大小
- 元素选择器

---

**注意**: 如果页面结构发生变化，可能需要更新元素选择器和数据提取逻辑。
