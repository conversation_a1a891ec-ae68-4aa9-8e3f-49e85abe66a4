# 故障排除指南

## 常见错误及解决方案

### 1. "Could not establish connection. Receiving end does not exist."

**错误描述**: 这是最常见的Chrome扩展通信错误，表示消息发送方和接收方之间无法建立连接。

**可能原因**:
- Content script未正确加载
- 页面URL不匹配manifest.json中的匹配规则
- 页面刷新后content script失效
- 在错误的页面使用插件

**解决方案**:
1. **检查页面URL**: 确保在正确的监测数据页面使用插件
   ```
   URL应包含: */Web6/MonitorControl/Enterprise/HistoryData.aspx*
   ```

2. **刷新页面**: 刷新监测数据页面，让content script重新加载

3. **重新加载插件**: 
   - 打开 `chrome://extensions/`
   - 找到插件，点击刷新按钮
   - 重新打开监测数据页面

4. **检查控制台错误**:
   - 按F12打开开发者工具
   - 查看Console标签页是否有错误信息

### 2. 插件图标不显示或无法点击

**可能原因**:
- 插件安装失败
- manifest.json配置错误
- 权限不足

**解决方案**:
1. **重新安装插件**:
   - 删除现有插件
   - 重新加载插件文件夹

2. **检查manifest.json**:
   - 确保文件格式正确
   - 检查权限配置

3. **检查文件完整性**:
   - 确保所有必需文件都存在
   - 检查文件路径是否正确

### 3. 页面元素无法识别

**错误表现**:
- 插件无法找到下拉选择框
- 查询按钮点击无效
- 数据表格无法读取

**解决方案**:
1. **使用测试页面**:
   - 打开 `test.html` 进行元素检测
   - 查看哪些元素缺失

2. **检查页面结构**:
   - 确认页面包含必要的元素ID
   - 检查是否有JavaScript错误影响页面加载

3. **更新元素选择器**:
   - 如果页面结构发生变化，需要更新content.js中的元素选择器

### 4. 数据分析不准确

**可能原因**:
- 分析规则不匹配
- 数据格式变化
- 参数映射错误

**解决方案**:
1. **检查分析规则**:
   - 查看 `rules.json` 中的参数配置
   - 确认阈值设置是否合理

2. **验证数据格式**:
   - 检查表格数据结构是否与预期一致
   - 确认数值解析是否正确

3. **调整参数映射**:
   - 更新 `rules.json` 中的 `parameterMapping`
   - 添加新的监测项目映射

### 5. 导出功能失败

**错误表现**:
- 点击导出无响应
- 导出文件为空
- 下载失败

**解决方案**:
1. **检查权限**:
   - 确认manifest.json中包含 `downloads` 权限
   - 检查浏览器下载设置

2. **验证数据**:
   - 确保有异常数据可导出
   - 检查分析结果是否正确生成

3. **尝试不同格式**:
   - 如果Excel导出失败，尝试CSV格式
   - 检查文件大小是否超限

## 调试步骤

### 1. 基础检查
```bash
1. 确认插件已正确安装并启用
2. 检查页面URL是否匹配
3. 刷新页面重新加载content script
4. 查看浏览器控制台错误信息
```

### 2. 通信测试
```javascript
// 在监测数据页面的控制台中执行
chrome.runtime.sendMessage({action: 'ping'}, (response) => {
    console.log('Response:', response);
});
```

### 3. 元素检测
```javascript
// 在监测数据页面的控制台中执行
const elements = [
    '#sel_SubType', '#sel_City', '#sel_ent', 
    '#select_sub', '#sel_Item', '#dataBasic'
];
elements.forEach(selector => {
    const element = document.querySelector(selector);
    console.log(selector, element ? '✓' : '✗');
});
```

### 4. 存储检查
```javascript
// 检查插件配置
chrome.storage.local.get('config', (result) => {
    console.log('Config:', result.config);
});
```

## 性能优化

### 1. 减少查询频率
- 增加查询间隔时间
- 减少批量查询的数量
- 避免重复查询相同条件

### 2. 优化内存使用
- 及时清理不需要的数据
- 限制分析结果的数量
- 避免长时间运行分析

### 3. 提高响应速度
- 使用异步操作
- 优化数据处理算法
- 减少DOM操作频率

## 兼容性问题

### 1. Chrome版本兼容性
- 确保使用Chrome 88+版本
- 检查Manifest V3兼容性
- 验证API可用性

### 2. 页面版本兼容性
- 确认页面使用的框架版本
- 检查JavaScript库兼容性
- 验证CSS选择器有效性

### 3. 系统兼容性
- Windows 10+推荐
- 确保有足够的内存和存储空间
- 检查网络连接稳定性

## 获取帮助

### 1. 日志收集
在报告问题时，请提供以下信息：
- Chrome版本号
- 插件版本号
- 错误截图
- 控制台错误日志
- 操作步骤描述

### 2. 测试环境
使用提供的 `test.html` 页面进行基础功能测试：
1. 打开test.html页面
2. 运行所有测试项目
3. 记录测试结果
4. 提供测试日志

### 3. 联系支持
- 邮箱: <EMAIL>
- 项目地址: https://github.com/example/monitoring-plugin
- 文档: 查看README.md和相关文档

## 预防措施

### 1. 定期更新
- 保持Chrome浏览器最新版本
- 及时更新插件版本
- 关注页面结构变化

### 2. 备份配置
- 定期导出插件配置
- 保存自定义分析规则
- 备份重要的分析结果

### 3. 测试验证
- 在生产环境使用前先测试
- 验证分析结果的准确性
- 确认导出功能正常

---

**注意**: 如果问题持续存在，请联系技术支持并提供详细的错误信息和操作步骤。
