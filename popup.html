<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境监测数据分析</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>环境监测数据自动化分析</h1>
        </header>
        
        <div class="section">
            <h3>查询配置</h3>
            <div class="form-group">
                <label for="timeRange">时间范围:</label>
                <select id="timeRange">
                    <option value="1">最近1小时</option>
                    <option value="6">最近6小时</option>
                    <option value="24" selected>最近24小时</option>
                    <option value="168">最近7天</option>
                    <option value="custom">自定义</option>
                </select>
            </div>
            
            <div id="customTimeRange" class="form-group" style="display: none;">
                <label for="startTime">开始时间:</label>
                <input type="datetime-local" id="startTime">
                <label for="endTime">结束时间:</label>
                <input type="datetime-local" id="endTime">
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="autoQuery" checked>
                    启用自动批量查询
                </label>
            </div>
        </div>
        
        <div class="section">
            <h3>分析规则</h3>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="enableAnalysis" checked>
                    启用数据异常分析
                </label>
            </div>
            
            <div class="form-group">
                <label for="thresholdType">异常检测阈值:</label>
                <select id="thresholdType">
                    <option value="standard">标准阈值</option>
                    <option value="strict">严格阈值</option>
                    <option value="custom">自定义阈值</option>
                </select>
            </div>
        </div>
        
        <div class="section">
            <h3>导出设置</h3>
            <div class="form-group">
                <label for="exportFormat">导出格式:</label>
                <select id="exportFormat">
                    <option value="excel">Excel (.xlsx)</option>
                    <option value="csv">CSV (.csv)</option>
                    <option value="json">JSON (.json)</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="includeCharts" checked>
                    包含图表分析
                </label>
            </div>
        </div>
        
        <div class="actions">
            <button id="startAnalysis" class="btn-primary">开始分析</button>
            <button id="stopAnalysis" class="btn-secondary" disabled>停止分析</button>
            <button id="viewResults" class="btn-info">查看结果</button>
        </div>
        
        <div class="status">
            <div id="progressBar" class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div id="statusText">就绪</div>
        </div>
        
        <div class="section">
            <h3>统计信息</h3>
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-label">已查询企业:</span>
                    <span id="queriedCompanies">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">检测到异常:</span>
                    <span id="anomaliesFound">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">处理进度:</span>
                    <span id="processProgress">0%</span>
                </div>
            </div>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
