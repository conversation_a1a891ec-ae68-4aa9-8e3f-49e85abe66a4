// 数据导出管理器
class ExportManager {
    constructor() {
        this.supportedFormats = ['excel', 'csv', 'json', 'pdf'];
        this.init();
    }

    init() {
        console.log('导出管理器初始化完成');
    }

    /**
     * 导出分析结果
     * @param {Object} analysisResult - 分析结果
     * @param {string} format - 导出格式
     * @param {Object} options - 导出选项
     * @returns {Promise} 导出Promise
     */
    async exportAnalysisResult(analysisResult, format = 'excel', options = {}) {
        if (!this.supportedFormats.includes(format)) {
            throw new Error(`不支持的导出格式: ${format}`);
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
        const filename = `环境监测异常分析报告_${timestamp}`;

        try {
            switch (format) {
                case 'excel':
                    return await this.exportToExcel(analysisResult, filename, options);
                case 'csv':
                    return await this.exportToCSV(analysisResult, filename, options);
                case 'json':
                    return await this.exportToJSON(analysisResult, filename, options);
                case 'pdf':
                    return await this.exportToPDF(analysisResult, filename, options);
                default:
                    throw new Error(`未实现的导出格式: ${format}`);
            }
        } catch (error) {
            console.error('导出失败:', error);
            throw error;
        }
    }

    /**
     * 导出为Excel格式
     * @param {Object} analysisResult - 分析结果
     * @param {string} filename - 文件名
     * @param {Object} options - 选项
     */
    async exportToExcel(analysisResult, filename, options) {
        // 创建Excel内容（简化版，实际应用中可使用SheetJS等库）
        const csvContent = this.generateCSVContent(analysisResult, options);
        
        // 使用CSV格式作为Excel的简化版本
        const blob = new Blob(['\ufeff' + csvContent], { 
            type: 'application/vnd.ms-excel;charset=utf-8' 
        });
        
        return await this.downloadFile(blob, `${filename}.csv`);
    }

    /**
     * 导出为CSV格式
     * @param {Object} analysisResult - 分析结果
     * @param {string} filename - 文件名
     * @param {Object} options - 选项
     */
    async exportToCSV(analysisResult, filename, options) {
        const csvContent = this.generateCSVContent(analysisResult, options);
        const blob = new Blob(['\ufeff' + csvContent], { 
            type: 'text/csv;charset=utf-8' 
        });
        
        return await this.downloadFile(blob, `${filename}.csv`);
    }

    /**
     * 导出为JSON格式
     * @param {Object} analysisResult - 分析结果
     * @param {string} filename - 文件名
     * @param {Object} options - 选项
     */
    async exportToJSON(analysisResult, filename, options) {
        const jsonContent = JSON.stringify(analysisResult, null, 2);
        const blob = new Blob([jsonContent], { 
            type: 'application/json;charset=utf-8' 
        });
        
        return await this.downloadFile(blob, `${filename}.json`);
    }

    /**
     * 导出为PDF格式
     * @param {Object} analysisResult - 分析结果
     * @param {string} filename - 文件名
     * @param {Object} options - 选项
     */
    async exportToPDF(analysisResult, filename, options) {
        // 生成HTML报告内容
        const htmlContent = this.generateHTMLReport(analysisResult, options);
        
        // 创建临时HTML文件用于打印
        const blob = new Blob([htmlContent], { 
            type: 'text/html;charset=utf-8' 
        });
        
        return await this.downloadFile(blob, `${filename}.html`);
    }

    /**
     * 生成CSV内容
     * @param {Object} analysisResult - 分析结果
     * @param {Object} options - 选项
     * @returns {string} CSV内容
     */
    generateCSVContent(analysisResult, options = {}) {
        const { anomalies, statistics, queryInfo, analysisTime, ruleType } = analysisResult;
        
        let csv = '';
        
        // 报告头部信息
        csv += '环境监测数据异常分析报告\n';
        csv += `分析时间,${analysisTime}\n`;
        csv += `规则类型,${ruleType}\n`;
        csv += `查询范围,"${JSON.stringify(queryInfo)}"\n`;
        csv += `总记录数,${statistics.totalRecords}\n`;
        csv += `异常记录数,${statistics.anomalyCount}\n`;
        csv += `异常率,${((statistics.anomalyCount / statistics.totalRecords) * 100).toFixed(2)}%\n`;
        csv += '\n';

        // 异常详情表头
        const headers = [
            '序号', '时间', '企业名称', '排口名称', '排口类型', 
            '监测项目', '监测值', '单位', '标准值', '异常类型', 
            '严重程度', '超标倍数', '描述'
        ];
        csv += headers.join(',') + '\n';

        // 异常详情数据
        anomalies.forEach((anomaly, index) => {
            const row = [
                index + 1,
                `"${anomaly.timestamp || ''}"`,
                `"${anomaly.company || ''}"`,
                `"${anomaly.outlet || ''}"`,
                `"${anomaly.outletType || ''}"`,
                `"${anomaly.parameter || ''}"`,
                anomaly.value || '',
                `"${anomaly.unit || ''}"`,
                anomaly.threshold || '',
                `"${this.getAnomalyTypeName(anomaly.anomalyType)}"`,
                `"${this.getSeverityName(anomaly.severity)}"`,
                anomaly.exceedRatio || '',
                `"${anomaly.description || ''}"`
            ];
            csv += row.join(',') + '\n';
        });

        // 统计信息
        csv += '\n统计信息\n';
        csv += '监测项目,异常次数\n';
        Object.entries(statistics.parameterStats || {}).forEach(([param, count]) => {
            csv += `"${param}",${count}\n`;
        });

        csv += '\n严重程度统计\n';
        csv += '严重程度,数量\n';
        Object.entries(statistics.severityStats || {}).forEach(([severity, count]) => {
            csv += `"${this.getSeverityName(severity)}",${count}\n`;
        });

        return csv;
    }

    /**
     * 生成HTML报告
     * @param {Object} analysisResult - 分析结果
     * @param {Object} options - 选项
     * @returns {string} HTML内容
     */
    generateHTMLReport(analysisResult, options = {}) {
        const { anomalies, statistics, queryInfo, analysisTime, ruleType } = analysisResult;
        
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环境监测数据异常分析报告</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { background: #f5f5f5; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary-item { display: inline-block; margin-right: 30px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #4CAF50; color: white; }
        .severity-high { background-color: #ffebee; }
        .severity-medium { background-color: #fff3e0; }
        .severity-low { background-color: #f3e5f5; }
        .chart-container { margin: 20px 0; }
        @media print { body { margin: 0; } }
    </style>
</head>
<body>
    <div class="header">
        <h1>环境监测数据异常分析报告</h1>
        <p>生成时间: ${analysisTime}</p>
        <p>分析规则: ${ruleType === 'standard' ? '国家标准' : '严格标准'}</p>
    </div>

    <div class="summary">
        <h2>概览信息</h2>
        <div class="summary-item"><strong>总记录数:</strong> ${statistics.totalRecords}</div>
        <div class="summary-item"><strong>异常记录数:</strong> ${statistics.anomalyCount}</div>
        <div class="summary-item"><strong>异常率:</strong> ${((statistics.anomalyCount / statistics.totalRecords) * 100).toFixed(2)}%</div>
        <div class="summary-item"><strong>查询范围:</strong> ${JSON.stringify(queryInfo)}</div>
    </div>

    <h2>异常详情</h2>
    <table>
        <thead>
            <tr>
                <th>序号</th><th>时间</th><th>企业名称</th><th>排口名称</th>
                <th>监测项目</th><th>监测值</th><th>标准值</th><th>异常类型</th>
                <th>严重程度</th><th>描述</th>
            </tr>
        </thead>
        <tbody>
            ${anomalies.map((anomaly, index) => `
                <tr class="severity-${anomaly.severity}">
                    <td>${index + 1}</td>
                    <td>${anomaly.timestamp || ''}</td>
                    <td>${anomaly.company || ''}</td>
                    <td>${anomaly.outlet || ''}</td>
                    <td>${anomaly.parameter || ''}</td>
                    <td>${anomaly.value || ''} ${anomaly.unit || ''}</td>
                    <td>${anomaly.threshold || ''}</td>
                    <td>${this.getAnomalyTypeName(anomaly.anomalyType)}</td>
                    <td>${this.getSeverityName(anomaly.severity)}</td>
                    <td>${anomaly.description || ''}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <h2>统计分析</h2>
    <div style="display: flex; justify-content: space-between;">
        <div style="width: 48%;">
            <h3>监测项目异常统计</h3>
            <table>
                <thead><tr><th>监测项目</th><th>异常次数</th></tr></thead>
                <tbody>
                    ${Object.entries(statistics.parameterStats || {}).map(([param, count]) => 
                        `<tr><td>${param}</td><td>${count}</td></tr>`
                    ).join('')}
                </tbody>
            </table>
        </div>
        <div style="width: 48%;">
            <h3>严重程度统计</h3>
            <table>
                <thead><tr><th>严重程度</th><th>数量</th></tr></thead>
                <tbody>
                    ${Object.entries(statistics.severityStats || {}).map(([severity, count]) => 
                        `<tr><td>${this.getSeverityName(severity)}</td><td>${count}</td></tr>`
                    ).join('')}
                </tbody>
            </table>
        </div>
    </div>

    <h2>建议措施</h2>
    <ul>
        ${statistics.severityStats.high > 0 ? 
            `<li><strong>紧急处理:</strong> 发现${statistics.severityStats.high}项高严重程度异常，建议立即进行现场检查和整改。</li>` : ''}
        <li><strong>加强监管:</strong> 建议对异常频率较高的企业和监测项目加强日常监管。</li>
        <li><strong>技术改进:</strong> 建议企业完善污染治理设施，提高处理效率。</li>
        <li><strong>持续监控:</strong> 建议建立长期监控机制，及时发现和处理异常情况。</li>
    </ul>
</body>
</html>`;
    }

    /**
     * 下载文件
     * @param {Blob} blob - 文件内容
     * @param {string} filename - 文件名
     */
    async downloadFile(blob, filename) {
        try {
            // 使用Chrome下载API
            const url = URL.createObjectURL(blob);
            
            const downloadId = await chrome.downloads.download({
                url: url,
                filename: filename,
                saveAs: true
            });

            // 清理URL对象
            setTimeout(() => URL.revokeObjectURL(url), 1000);
            
            return { success: true, downloadId: downloadId, filename: filename };
        } catch (error) {
            console.error('下载失败:', error);
            throw error;
        }
    }

    /**
     * 获取异常类型名称
     * @param {string} type - 异常类型
     * @returns {string} 中文名称
     */
    getAnomalyTypeName(type) {
        const typeNames = {
            'exceed_max': '超过最大值',
            'below_min': '低于最小值',
            'trend_abnormal': '趋势异常',
            'missing_data': '数据缺失'
        };
        return typeNames[type] || type;
    }

    /**
     * 获取严重程度名称
     * @param {string} severity - 严重程度
     * @returns {string} 中文名称
     */
    getSeverityName(severity) {
        const severityNames = {
            'high': '高',
            'medium': '中',
            'low': '低'
        };
        return severityNames[severity] || severity;
    }

    /**
     * 批量导出多个分析结果
     * @param {Array} analysisResults - 分析结果数组
     * @param {string} format - 导出格式
     * @param {Object} options - 选项
     */
    async batchExport(analysisResults, format = 'excel', options = {}) {
        const results = [];
        
        for (let i = 0; i < analysisResults.length; i++) {
            const result = analysisResults[i];
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
            const filename = `环境监测异常分析报告_${i + 1}_${timestamp}`;
            
            try {
                const exportResult = await this.exportAnalysisResult(result, format, {
                    ...options,
                    filename: filename
                });
                results.push(exportResult);
            } catch (error) {
                console.error(`批量导出第${i + 1}个文件失败:`, error);
                results.push({ success: false, error: error.message });
            }
        }
        
        return results;
    }
}

// 导出管理器实例
if (typeof window !== 'undefined') {
    window.ExportManager = ExportManager;
}
