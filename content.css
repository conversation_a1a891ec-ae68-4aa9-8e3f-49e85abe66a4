/* 环境监测数据分析插件 - Content Script 样式 */

#monitoring-analysis-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 300px;
    background: white;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 99999;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    font-size: 12px;
}

.panel-header {
    background: #4CAF50;
    color: white;
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 6px 6px 0 0;
    font-weight: 600;
}

#panel-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#panel-toggle:hover {
    background: rgba(255,255,255,0.2);
    border-radius: 3px;
}

.panel-content {
    padding: 12px;
}

.status-info {
    margin-bottom: 12px;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
}

.status-info div {
    margin-bottom: 4px;
    display: flex;
    justify-content: space-between;
}

.status-info div:last-child {
    margin-bottom: 0;
}

.status-info span {
    font-weight: 600;
    color: #333;
}

.panel-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.panel-actions button {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

#start-analysis-btn {
    background: #4CAF50;
    color: white;
}

#start-analysis-btn:hover:not(:disabled) {
    background: #45a049;
}

#stop-analysis-btn {
    background: #f44336;
    color: white;
}

#stop-analysis-btn:hover:not(:disabled) {
    background: #da190b;
}

#export-results-btn {
    background: #2196F3;
    color: white;
}

#export-results-btn:hover:not(:disabled) {
    background: #0b7dda;
}

.panel-actions button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* EasyUI DataGrid 异常数据高亮样式 */
.datagrid-row.anomaly-row {
    background-color: #ffebee !important;
}

.datagrid-row.anomaly-row:hover {
    background-color: #ffcdd2 !important;
}

.datagrid-cell.anomaly-cell {
    background-color: #ffcdd2 !important;
    font-weight: bold !important;
    color: #c62828 !important;
}

/* 高严重程度异常 */
.datagrid-row.anomaly-high {
    background-color: #ffebee !important;
    border-left: 4px solid #f44336 !important;
}

/* 中等严重程度异常 */
.datagrid-row.anomaly-medium {
    background-color: #fff3e0 !important;
    border-left: 4px solid #ff9800 !important;
}

/* 低严重程度异常 */
.datagrid-row.anomaly-low {
    background-color: #f3e5f5 !important;
    border-left: 4px solid #9c27b0 !important;
}

/* 分析进度指示器 */
.analysis-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 20px;
    border-radius: 8px;
    z-index: 10001;
    text-align: center;
    min-width: 200px;
}

.analysis-indicator .spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4CAF50;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 768px) {
    #monitoring-analysis-panel {
        width: 250px;
        right: 10px;
        top: 10px;
    }
}

/* 确保面板在所有页面元素之上 */
#monitoring-analysis-panel * {
    box-sizing: border-box;
}

/* 防止页面样式影响插件面板 */
#monitoring-analysis-panel,
#monitoring-analysis-panel * {
    all: unset;
    font-family: 'Microsoft YaHei', Arial, sans-serif !important;
}

#monitoring-analysis-panel {
    display: block !important;
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 280px !important;
    background: white !important;
    border: 2px solid #4CAF50 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    z-index: 10000 !important;
    font-size: 12px !important;
}

/* 重新应用必要的样式 */
#monitoring-analysis-panel .panel-header {
    display: flex !important;
    background: #4CAF50 !important;
    color: white !important;
    padding: 8px 12px !important;
    justify-content: space-between !important;
    align-items: center !important;
    border-radius: 6px 6px 0 0 !important;
    font-weight: 600 !important;
}

#monitoring-analysis-panel .panel-content {
    display: block !important;
    padding: 12px !important;
}

#monitoring-analysis-panel .status-info {
    display: block !important;
    margin-bottom: 12px !important;
    padding: 8px !important;
    background: #f5f5f5 !important;
    border-radius: 4px !important;
}

#monitoring-analysis-panel .status-info div {
    display: flex !important;
    justify-content: space-between !important;
    margin-bottom: 4px !important;
}

#monitoring-analysis-panel .panel-actions {
    display: flex !important;
    flex-direction: column !important;
    gap: 6px !important;
}

#monitoring-analysis-panel .panel-actions button {
    display: block !important;
    padding: 6px 12px !important;
    border: none !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    color: white !important;
    text-align: center !important;
}
