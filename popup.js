// 环境监测数据分析插件 - Popup界面控制
class PopupController {
    constructor() {
        this.isAnalyzing = false;
        this.currentProgress = 0;
        this.analysisResults = [];
        this.config = {};
        this.init();
    }

    async init() {
        // 加载保存的配置
        await this.loadConfig();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 初始化界面状态
        this.updateUI();
        
        // 监听来自background的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleBackgroundMessage(message);
        });
        
        console.log('Popup控制器初始化完成');
    }

    async loadConfig() {
        try {
            const result = await chrome.storage.local.get('config');
            this.config = result.config || this.getDefaultConfig();
            this.applyConfigToUI();
        } catch (error) {
            console.error('加载配置失败:', error);
            this.config = this.getDefaultConfig();
        }
    }

    getDefaultConfig() {
        return {
            timeRange: '24',
            autoQuery: true,
            enableAnalysis: true,
            thresholdType: 'standard',
            exportFormat: 'excel',
            includeCharts: true
        };
    }

    applyConfigToUI() {
        // 应用配置到界面元素
        document.getElementById('timeRange').value = this.config.timeRange;
        document.getElementById('autoQuery').checked = this.config.autoQuery;
        document.getElementById('enableAnalysis').checked = this.config.enableAnalysis;
        document.getElementById('thresholdType').value = this.config.thresholdType;
        document.getElementById('exportFormat').value = this.config.exportFormat;
        document.getElementById('includeCharts').checked = this.config.includeCharts;
        
        // 处理自定义时间范围
        this.toggleCustomTimeRange();
    }

    bindEvents() {
        // 时间范围选择
        document.getElementById('timeRange').addEventListener('change', (e) => {
            this.config.timeRange = e.target.value;
            this.toggleCustomTimeRange();
            this.saveConfig();
        });

        // 自定义时间范围显示/隐藏
        this.toggleCustomTimeRange();

        // 配置选项变化
        ['autoQuery', 'enableAnalysis', 'includeCharts'].forEach(id => {
            document.getElementById(id).addEventListener('change', (e) => {
                this.config[id] = e.target.checked;
                this.saveConfig();
            });
        });

        ['thresholdType', 'exportFormat'].forEach(id => {
            document.getElementById(id).addEventListener('change', (e) => {
                this.config[id] = e.target.value;
                this.saveConfig();
            });
        });

        // 自定义时间
        ['startTime', 'endTime'].forEach(id => {
            document.getElementById(id).addEventListener('change', (e) => {
                this.config[id] = e.target.value;
                this.saveConfig();
            });
        });

        // 操作按钮
        document.getElementById('startAnalysis').addEventListener('click', () => {
            this.startAnalysis();
        });

        document.getElementById('stopAnalysis').addEventListener('click', () => {
            this.stopAnalysis();
        });

        document.getElementById('viewResults').addEventListener('click', () => {
            this.viewResults();
        });
    }

    toggleCustomTimeRange() {
        const customTimeRange = document.getElementById('customTimeRange');
        const timeRange = document.getElementById('timeRange').value;
        
        if (timeRange === 'custom') {
            customTimeRange.style.display = 'block';
            // 设置默认时间
            if (!this.config.startTime || !this.config.endTime) {
                const now = new Date();
                const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                
                document.getElementById('startTime').value = this.formatDateTimeLocal(yesterday);
                document.getElementById('endTime').value = this.formatDateTimeLocal(now);
            }
        } else {
            customTimeRange.style.display = 'none';
        }
    }

    formatDateTimeLocal(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    async saveConfig() {
        try {
            await chrome.storage.local.set({ config: this.config });
        } catch (error) {
            console.error('保存配置失败:', error);
        }
    }

    async startAnalysis() {
        if (this.isAnalyzing) {
            this.showMessage('分析已在进行中', 'warning');
            return;
        }

        try {
            // 检查是否在正确的页面
            const tabs = await chrome.tabs.query({
                active: true,
                currentWindow: true,
                url: '*://*/Web6/MonitorControl/Enterprise/HistoryData.aspx*'
            });

            if (tabs.length === 0) {
                this.showMessage('请先打开环境监测数据页面', 'error');
                return;
            }

            // 更新配置
            await this.updateConfigFromUI();

            // 直接向content script发送消息，而不是通过background
            const response = await chrome.tabs.sendMessage(tabs[0].id, {
                action: 'startAnalysis',
                config: this.config
            });

            if (response && response.success) {
                this.isAnalyzing = true;
                this.updateAnalysisState();
                this.showMessage('分析已开始', 'success');
            } else {
                this.showMessage(response?.error || '启动分析失败', 'error');
            }
        } catch (error) {
            console.error('启动分析失败:', error);
            if (error.message.includes('Could not establish connection')) {
                this.showMessage('请确保在环境监测数据页面使用插件', 'error');
            } else {
                this.showMessage('启动分析失败: ' + error.message, 'error');
            }
        }
    }

    async stopAnalysis() {
        try {
            // 获取当前活动标签页
            const tabs = await chrome.tabs.query({
                active: true,
                currentWindow: true,
                url: '*://*/Web6/MonitorControl/Enterprise/HistoryData.aspx*'
            });

            if (tabs.length > 0) {
                const response = await chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'stopAnalysis'
                });

                if (response && response.success) {
                    this.isAnalyzing = false;
                    this.updateAnalysisState();
                    this.showMessage('分析已停止', 'info');
                }
            } else {
                this.isAnalyzing = false;
                this.updateAnalysisState();
                this.showMessage('分析已停止', 'info');
            }
        } catch (error) {
            console.error('停止分析失败:', error);
            // 即使通信失败，也要更新本地状态
            this.isAnalyzing = false;
            this.updateAnalysisState();
            this.showMessage('分析已停止', 'info');
        }
    }

    async updateConfigFromUI() {
        this.config.timeRange = document.getElementById('timeRange').value;
        this.config.autoQuery = document.getElementById('autoQuery').checked;
        this.config.enableAnalysis = document.getElementById('enableAnalysis').checked;
        this.config.thresholdType = document.getElementById('thresholdType').value;
        this.config.exportFormat = document.getElementById('exportFormat').value;
        this.config.includeCharts = document.getElementById('includeCharts').checked;

        if (this.config.timeRange === 'custom') {
            this.config.startTime = document.getElementById('startTime').value;
            this.config.endTime = document.getElementById('endTime').value;
        }

        await this.saveConfig();
    }

    updateAnalysisState() {
        const startBtn = document.getElementById('startAnalysis');
        const stopBtn = document.getElementById('stopAnalysis');
        const statusText = document.getElementById('statusText');

        if (this.isAnalyzing) {
            startBtn.disabled = true;
            stopBtn.disabled = false;
            statusText.textContent = '分析中...';
            statusText.classList.add('analyzing');
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            statusText.textContent = '就绪';
            statusText.classList.remove('analyzing');
        }
    }

    handleBackgroundMessage(message) {
        switch (message.type) {
            case 'progressUpdate':
                this.updateProgress(message.progress, message.results);
                break;
            case 'analysisComplete':
                this.onAnalysisComplete(message.results);
                break;
            case 'analysisError':
                this.onAnalysisError(message.error);
                break;
        }
    }

    updateProgress(progress, results) {
        this.currentProgress = progress;
        this.analysisResults = results || [];

        // 更新进度条
        const progressFill = document.getElementById('progressFill');
        const processProgress = document.getElementById('processProgress');
        
        progressFill.style.width = `${progress}%`;
        processProgress.textContent = `${Math.round(progress)}%`;

        // 更新统计信息
        this.updateStatistics();
    }

    updateStatistics() {
        const queriedCompanies = new Set();
        let anomaliesCount = 0;

        this.analysisResults.forEach(result => {
            if (result.company) {
                queriedCompanies.add(result.company);
            }
            if (result.anomalies) {
                anomaliesCount += result.anomalies.length;
            }
        });

        document.getElementById('queriedCompanies').textContent = queriedCompanies.size;
        document.getElementById('anomaliesFound').textContent = anomaliesCount;
    }

    onAnalysisComplete(results) {
        this.isAnalyzing = false;
        this.analysisResults = results;
        this.updateAnalysisState();
        this.updateStatistics();
        this.showMessage('分析完成', 'success');
    }

    onAnalysisError(error) {
        this.isAnalyzing = false;
        this.updateAnalysisState();
        this.showMessage('分析出错: ' + error, 'error');
    }

    async viewResults() {
        if (this.analysisResults.length === 0) {
            this.showMessage('暂无分析结果', 'info');
            return;
        }

        // 导出结果
        try {
            // 获取当前活动标签页
            const tabs = await chrome.tabs.query({
                active: true,
                currentWindow: true,
                url: '*://*/Web6/MonitorControl/Enterprise/HistoryData.aspx*'
            });

            if (tabs.length > 0) {
                const response = await chrome.tabs.sendMessage(tabs[0].id, {
                    action: 'exportResults',
                    format: this.config.exportFormat
                });

                if (response && response.success) {
                    this.showMessage('结果已导出', 'success');
                } else {
                    this.showMessage('导出失败: ' + (response?.error || '未知错误'), 'error');
                }
            } else {
                this.showMessage('请在环境监测数据页面使用此功能', 'error');
            }
        } catch (error) {
            console.error('导出结果失败:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        }
    }

    showMessage(message, type = 'info') {
        // 创建消息提示
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message-${type}`;
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            padding: 8px 16px;
            border-radius: 4px;
            color: white;
            font-size: 12px;
            z-index: 1000;
            background: ${this.getMessageColor(type)};
        `;

        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }

    getMessageColor(type) {
        const colors = {
            'success': '#4CAF50',
            'error': '#f44336',
            'warning': '#ff9800',
            'info': '#2196F3'
        };
        return colors[type] || colors.info;
    }

    updateUI() {
        // 定期更新进度
        setInterval(async () => {
            if (this.isAnalyzing) {
                try {
                    // 获取当前活动标签页
                    const tabs = await chrome.tabs.query({
                        active: true,
                        currentWindow: true,
                        url: '*://*/Web6/MonitorControl/Enterprise/HistoryData.aspx*'
                    });

                    if (tabs.length > 0) {
                        const response = await chrome.tabs.sendMessage(tabs[0].id, {
                            action: 'getProgress'
                        });

                        if (response && response.progress !== undefined) {
                            this.updateProgress(response.progress, response.results);
                        }

                        if (!response.isAnalyzing && this.isAnalyzing) {
                            this.onAnalysisComplete(response.results);
                        }
                    }
                } catch (error) {
                    // 如果通信失败，可能是页面已关闭或切换
                    // 忽略错误，继续监控
                }
            }
        }, 2000); // 增加间隔到2秒，减少通信频率
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});
