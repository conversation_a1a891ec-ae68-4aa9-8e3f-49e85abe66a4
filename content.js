// 环境监测数据自动化查询和分析 - Content Script
class MonitoringDataAnalyzer {
    constructor() {
        this.isAnalyzing = false;
        this.config = null;
        this.currentQueryIndex = 0;
        this.queryQueue = [];
        this.analysisResults = [];
        this.init();
    }

    init() {
        // 监听来自background的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializePage());
        } else {
            this.initializePage();
        }
    }

    initializePage() {
        // 等待页面完全加载
        this.waitForPageReady().then(() => {
            // 添加分析控制面板
            this.addAnalysisPanel();

            // 检测页面元素
            this.detectPageElements();

            // 设置页面特定的配置
            this.setupPageSpecificSettings();

            console.log('环境监测数据分析插件已加载');
        });
    }

    async waitForPageReady() {
        // 等待页面关键元素加载完成
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 20;

            const checkReady = () => {
                attempts++;

                // 检查关键元素是否存在
                const hasSelects = document.querySelector('#sel_SubType') &&
                                 document.querySelector('#sel_City') &&
                                 document.querySelector('#sel_ent');
                const hasDataGrid = document.querySelector('#dataBasic');
                const hasHistoryReport = typeof HistoryReport !== 'undefined';

                if (hasSelects && hasDataGrid && hasHistoryReport) {
                    resolve();
                    return;
                }

                if (attempts >= maxAttempts) {
                    console.warn('页面元素加载超时，继续初始化');
                    resolve();
                    return;
                }

                setTimeout(checkReady, 500);
            };

            checkReady();
        });
    }

    setupPageSpecificSettings() {
        // 设置默认时间类型为小时
        const hourRadio = document.querySelector('input[name="myRadio"][value="1"]');
        if (hourRadio && !hourRadio.checked) {
            hourRadio.click();
        }

        // 设置隐藏故障数据
        const hideInvalidData = document.querySelector('#chk_ShowInvalidData');
        if (hideInvalidData && !hideInvalidData.checked) {
            hideInvalidData.click();
        }
    }

    addAnalysisPanel() {
        // 创建浮动控制面板
        const panel = document.createElement('div');
        panel.id = 'monitoring-analysis-panel';
        panel.innerHTML = `
            <div class="panel-header">
                <span>数据分析控制</span>
                <button id="panel-toggle">−</button>
            </div>
            <div class="panel-content">
                <div class="status-info">
                    <div>状态: <span id="analysis-status">就绪</span></div>
                    <div>进度: <span id="analysis-progress">0%</span></div>
                    <div>异常: <span id="anomaly-count">0</span></div>
                </div>
                <div class="panel-actions">
                    <button id="start-analysis-btn">开始分析</button>
                    <button id="stop-analysis-btn" disabled>停止分析</button>
                    <button id="export-results-btn">导出结果</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        this.bindPanelEvents();
    }

    bindPanelEvents() {
        // 面板切换
        document.getElementById('panel-toggle').addEventListener('click', () => {
            const content = document.querySelector('.panel-content');
            const toggle = document.getElementById('panel-toggle');
            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.textContent = '−';
            } else {
                content.style.display = 'none';
                toggle.textContent = '+';
            }
        });

        // 开始分析
        document.getElementById('start-analysis-btn').addEventListener('click', () => {
            this.startLocalAnalysis();
        });

        // 停止分析
        document.getElementById('stop-analysis-btn').addEventListener('click', () => {
            this.stopAnalysis();
        });

        // 导出结果
        document.getElementById('export-results-btn').addEventListener('click', () => {
            this.exportResults();
        });
    }

    detectPageElements() {
        // 根据实际页面结构检测关键元素
        this.elements = {
            // 排口类型选择器
            outletType: document.querySelector('#sel_SubType'),
            // 所属地区选择器
            region: document.querySelector('#sel_City'),
            // 企业名称选择器
            company: document.querySelector('#sel_ent'),
            // 排口名称选择器
            outlet: document.querySelector('#select_sub'),
            // 监测项目选择器
            parameter: document.querySelector('#sel_Item'),
            // 时间选择器（根据当前选中的时间类型）
            startTime: this.getCurrentTimeInput('start'),
            endTime: this.getCurrentTimeInput('end'),
            // 查询按钮
            queryButton: document.querySelector('button[onclick*="HistoryReport.doSearch"]'),
            // 数据表格
            dataTable: document.querySelector('#dataBasic'),
            // 时间类型单选按钮
            timeTypeRadios: document.querySelectorAll('input[name="myRadio"]'),
            // 其他控制元素
            hideInvalidData: document.querySelector('#chk_ShowInvalidData'),
            showUploadData: document.querySelector('#chk_UploadData')
        };

        console.log('检测到的页面元素:', this.elements);
    }

    getCurrentTimeInput(type) {
        // 根据当前选中的时间类型返回对应的时间输入框
        const checkedRadio = document.querySelector('input[name="myRadio"]:checked');
        if (!checkedRadio) return null;

        const timeType = checkedRadio.value;
        const timeInputs = {
            '-1': { start: '#startMin', end: '#endMin' },     // 实时
            '0': { start: '#startMin', end: '#endMin' },      // 分钟
            '1': { start: '#startHours', end: '#endHours' },  // 小时
            '2': { start: '#startDays', end: '#endDays' },    // 日
            '3': { start: '#startMonth', end: '#endMonth' },  // 月
            '4': { start: '#startYear', end: '#endYear' }     // 年
        };

        const selector = timeInputs[timeType] ? timeInputs[timeType][type] : '#startHours';
        return document.querySelector(selector);
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'startAnalysis':
                    await this.startAnalysis(request.config);
                    sendResponse({ success: true });
                    break;

                case 'stopAnalysis':
                    this.stopAnalysis();
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ error: error.message });
        }
    }

    async startAnalysis(config) {
        if (this.isAnalyzing) {
            throw new Error('分析已在进行中');
        }

        this.config = config;
        this.isAnalyzing = true;
        this.currentQueryIndex = 0;
        this.analysisResults = [];

        this.updateStatus('正在准备查询队列...');
        
        // 生成查询队列
        await this.generateQueryQueue();
        
        this.updateStatus('开始批量查询...');
        
        // 开始批量查询
        await this.processBatchQueries();
    }

    async generateQueryQueue() {
        this.queryQueue = [];
        
        // 获取所有可选项
        const outletTypes = this.getSelectOptions(this.elements.outletType);
        const regions = this.getSelectOptions(this.elements.region);
        
        // 生成所有组合
        for (const outletType of outletTypes) {
            for (const region of regions) {
                // 设置排口类型和地区
                await this.setSelectValue(this.elements.outletType, outletType.value);
                await this.setSelectValue(this.elements.region, region.value);
                
                // 等待企业列表更新
                await this.waitForUpdate();
                
                // 获取企业列表
                const companies = this.getSelectOptions(this.elements.company);
                
                for (const company of companies) {
                    // 设置企业
                    await this.setSelectValue(this.elements.company, company.value);
                    
                    // 等待排口列表更新
                    await this.waitForUpdate();
                    
                    // 获取排口列表
                    const outlets = this.getSelectOptions(this.elements.outlet);
                    
                    for (const outlet of outlets) {
                        this.queryQueue.push({
                            outletType: outletType,
                            region: region,
                            company: company,
                            outlet: outlet
                        });
                    }
                }
            }
        }
        
        console.log(`生成查询队列，共${this.queryQueue.length}个查询任务`);
    }

    async processBatchQueries() {
        const total = this.queryQueue.length;
        
        for (let i = 0; i < total && this.isAnalyzing; i++) {
            const query = this.queryQueue[i];
            this.currentQueryIndex = i;
            
            this.updateStatus(`正在查询 ${i + 1}/${total}: ${query.company.text}`);
            this.updateProgress((i / total) * 100);
            
            try {
                await this.executeQuery(query);
                await this.analyzeCurrentData(query);
                
                // 查询间隔，避免请求过于频繁
                await this.delay(1000);
                
            } catch (error) {
                console.error(`查询失败:`, error);
                // 继续下一个查询
            }
        }
        
        if (this.isAnalyzing) {
            this.updateStatus('分析完成');
            this.updateProgress(100);
            this.isAnalyzing = false;
        }
    }

    async executeQuery(query) {
        try {
            // 设置排口类型
            await this.setSelectValue(this.elements.outletType, query.outletType.value);
            // 等待地区列表更新
            await this.waitForUpdate();

            // 设置所属地区
            await this.setSelectValue(this.elements.region, query.region.value);
            // 等待企业列表更新
            await this.waitForUpdate();

            // 设置企业名称
            await this.setSelectValue(this.elements.company, query.company.value);
            // 等待排口列表更新
            await this.waitForUpdate();

            // 设置排口名称
            await this.setSelectValue(this.elements.outlet, query.outlet.value);
            // 等待监测项目列表更新
            await this.waitForUpdate();

            // 设置监测项目（多选）
            if (this.elements.parameter) {
                // 获取所有可选项
                const options = this.getSelectOptions(this.elements.parameter);
                if (options.length > 0) {
                    // 选择所有监测项目
                    const values = options.map(opt => opt.value);
                    await this.setSelectValue(this.elements.parameter, values);
                }
            }

            // 设置时间范围
            await this.setTimeRange();

            // 点击查询按钮
            if (this.elements.queryButton) {
                this.elements.queryButton.click();
            } else {
                // 如果找不到按钮，尝试调用页面的查询函数
                if (typeof HistoryReport !== 'undefined' && typeof HistoryReport.doSearch === 'function') {
                    HistoryReport.doSearch();
                }
            }

            // 等待数据加载
            await this.waitForDataLoad();

            return true;
        } catch (error) {
            console.error('执行查询失败:', error);
            return false;
        }
    }

    async setTimeRange() {
        // 获取当前选中的时间类型
        const checkedRadio = document.querySelector('input[name="myRadio"]:checked');
        const timeType = checkedRadio ? checkedRadio.value : '1'; // 默认小时

        // 更新时间输入元素引用
        this.elements.startTime = this.getCurrentTimeInput('start');
        this.elements.endTime = this.getCurrentTimeInput('end');

        if (!this.elements.startTime || !this.elements.endTime) {
            console.error('找不到时间输入框');
            return;
        }

        if (this.config.timeRange === 'custom') {
            // 使用自定义时间范围
            if (this.elements.startTime.id.includes('Hours')) {
                // 小时格式
                this.elements.startTime.value = this.config.startTime;
                this.elements.endTime.value = this.config.endTime;
            } else {
                // 其他格式，可能需要转换
                this.elements.startTime.value = this.config.startTime;
                this.elements.endTime.value = this.config.endTime;
            }
        } else {
            // 使用预设时间范围
            const endTime = new Date();
            const startTime = new Date(endTime.getTime() - this.config.timeRange * 60 * 60 * 1000);

            // 根据时间类型格式化日期
            const format = this.getDateFormatByTimeType(timeType);
            this.elements.startTime.value = this.formatDateByType(startTime, format);
            this.elements.endTime.value = this.formatDateByType(endTime, format);
        }

        // 触发日期选择器的change事件
        this.elements.startTime.dispatchEvent(new Event('change', { bubbles: true }));
        this.elements.endTime.dispatchEvent(new Event('change', { bubbles: true }));
    }

    async analyzeCurrentData(query) {
        // 获取当前页面的数据表格
        const dataRows = this.extractTableData();
        
        if (dataRows.length === 0) {
            return;
        }
        
        // 分析数据异常
        const anomalies = this.detectAnomalies(dataRows);
        
        if (anomalies.length > 0) {
            const result = {
                timestamp: new Date().toISOString(),
                outletType: query.outletType.text,
                region: query.region.text,
                company: query.company.text,
                outlet: query.outlet.text,
                dataCount: dataRows.length,
                anomalies: anomalies
            };
            
            this.analysisResults.push(result);
            
            // 发送结果到background
            chrome.runtime.sendMessage({
                action: 'dataAnalyzed',
                data: result
            });
            
            this.updateAnomalyCount();

            // 高亮显示异常数据
            this.highlightAnomalies(anomalies);
        }
    }

    highlightAnomalies(anomalies) {
        if (!this.elements.dataTable || anomalies.length === 0) return;

        try {
            // 对于EasyUI DataGrid，需要特殊处理高亮
            if (typeof $ !== 'undefined' && $(this.elements.dataTable).data('datagrid')) {
                // 获取所有行
                const rows = $(this.elements.dataTable).datagrid('getRows');

                anomalies.forEach(anomaly => {
                    // 找到对应的行索引
                    const rowIndex = rows.findIndex(row => {
                        return row.time === anomaly.timestamp &&
                               row.parameter === anomaly.parameter;
                    });

                    if (rowIndex >= 0) {
                        // 添加异常样式类
                        const rowElement = $(this.elements.dataTable).datagrid('getPanel').find(`.datagrid-body tr[datagrid-row-index="${rowIndex}"]`);
                        if (rowElement.length > 0) {
                            rowElement.addClass(`anomaly-row anomaly-${anomaly.severity}`);

                            // 高亮异常值单元格
                            const valueCell = rowElement.find('td[field="value"] .datagrid-cell');
                            if (valueCell.length > 0) {
                                valueCell.addClass('anomaly-cell');
                                valueCell.attr('title', anomaly.description);
                            }
                        }
                    }
                });
            } else {
                // 备用方案：直接操作DOM
                const tbody = this.elements.dataTable.querySelector('.datagrid-body tbody');
                if (tbody) {
                    const rows = tbody.querySelectorAll('tr');

                    anomalies.forEach(anomaly => {
                        // 简单的行匹配逻辑
                        rows.forEach((row, index) => {
                            if (anomaly.rowIndex === index) {
                                row.classList.add('anomaly-row', `anomaly-${anomaly.severity}`);

                                // 高亮值单元格
                                const cells = row.querySelectorAll('td .datagrid-cell');
                                if (cells.length > 2) {
                                    cells[2].classList.add('anomaly-cell');
                                    cells[2].title = anomaly.description;
                                }
                            }
                        });
                    });
                }
            }
        } catch (error) {
            console.error('高亮异常数据失败:', error);
        }
    }

    // 工具方法
    getSelectOptions(selectElement) {
        if (!selectElement) return [];

        // 对于bootstrap-select组件，需要特殊处理
        if (selectElement.classList.contains('selectpicker')) {
            // 获取原始select的options
            return Array.from(selectElement.options)
                .filter(option => option.value && option.value !== '' && option.value !== 'all')
                .map(option => ({
                    value: option.value,
                    text: option.text.trim()
                }));
        } else {
            return Array.from(selectElement.options)
                .filter(option => option.value && option.value !== '')
                .map(option => ({
                    value: option.value,
                    text: option.text.trim()
                }));
        }
    }

    async setSelectValue(selectElement, value) {
        if (!selectElement) return;

        // 对于bootstrap-select组件，需要特殊处理
        if (selectElement.classList.contains('selectpicker')) {
            // 设置值
            $(selectElement).selectpicker('val', value);
            // 触发change事件
            $(selectElement).trigger('change');
        } else {
            selectElement.value = value;
            selectElement.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }

    extractTableData() {
        // EasyUI DataGrid 的数据提取方式
        if (!this.elements.dataTable) return [];

        try {
            // 尝试使用EasyUI的API获取数据
            if (typeof $ !== 'undefined' && $(this.elements.dataTable).data('datagrid')) {
                const rows = $(this.elements.dataTable).datagrid('getRows');
                return rows || [];
            }

            // 备用方案：直接从DOM提取
            const tbody = this.elements.dataTable.querySelector('.datagrid-body tbody');
            if (!tbody) return [];

            const rows = tbody.querySelectorAll('tr');
            const data = [];

            rows.forEach(row => {
                const cells = row.querySelectorAll('td .datagrid-cell');
                if (cells.length > 0) {
                    const rowData = Array.from(cells).map(cell => cell.textContent.trim());
                    data.push(rowData);
                }
            });

            return data;
        } catch (error) {
            console.error('提取表格数据失败:', error);
            return [];
        }
    }

    detectAnomalies(dataRows) {
        const anomalies = [];
        const rules = this.config.analysisRules[this.config.thresholdType];
        
        dataRows.forEach(row => {
            // 根据实际数据结构调整索引
            const parameter = row[4]; // 监测项目
            const value = parseFloat(row[5]); // 监测值
            
            if (isNaN(value)) return;
            
            // 检查各项指标
            Object.keys(rules).forEach(ruleKey => {
                if (parameter.toLowerCase().includes(ruleKey)) {
                    const rule = rules[ruleKey];
                    let anomalyType = null;
                    
                    if (rule.min !== undefined && value < rule.min) {
                        anomalyType = '低于最小值';
                    } else if (rule.max !== undefined && value > rule.max) {
                        anomalyType = '超过最大值';
                    }
                    
                    if (anomalyType) {
                        anomalies.push({
                            parameter: parameter,
                            value: value,
                            threshold: rule.min !== undefined ? `${rule.min}-${rule.max}` : `≤${rule.max}`,
                            type: anomalyType,
                            severity: value > rule.max * 2 ? '严重' : '轻微'
                        });
                    }
                }
            });
        });
        
        return anomalies;
    }

    // UI更新方法
    updateStatus(status) {
        const statusElement = document.getElementById('analysis-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    updateProgress(progress) {
        const progressElement = document.getElementById('analysis-progress');
        if (progressElement) {
            progressElement.textContent = `${Math.round(progress)}%`;
        }
    }

    updateAnomalyCount() {
        const countElement = document.getElementById('anomaly-count');
        if (countElement) {
            const totalAnomalies = this.analysisResults.reduce((sum, result) => 
                sum + result.anomalies.length, 0);
            countElement.textContent = totalAnomalies;
        }
    }

    // 辅助方法
    async waitForUpdate() {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async waitForDataLoad() {
        // 等待EasyUI DataGrid加载完成
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 30; // 最多等待30秒

            const checkDataLoad = () => {
                attempts++;

                try {
                    // 检查DataGrid是否已加载
                    if (typeof $ !== 'undefined' && $(this.elements.dataTable).data('datagrid')) {
                        const rows = $(this.elements.dataTable).datagrid('getRows');
                        if (rows && rows.length > 0) {
                            console.log(`数据加载完成，共${rows.length}行数据`);
                            resolve();
                            return;
                        }
                    }

                    // 备用检查：查看表格是否有数据行
                    const tbody = this.elements.dataTable?.querySelector('.datagrid-body tbody');
                    if (tbody && tbody.children.length > 0) {
                        console.log('数据加载完成（备用检查）');
                        resolve();
                        return;
                    }

                    if (attempts >= maxAttempts) {
                        console.log('等待数据加载超时');
                        resolve();
                        return;
                    }

                    // 继续等待
                    setTimeout(checkDataLoad, 1000);
                } catch (error) {
                    console.error('检查数据加载状态失败:', error);
                    if (attempts >= maxAttempts) {
                        resolve();
                    } else {
                        setTimeout(checkDataLoad, 1000);
                    }
                }
            };

            // 开始检查
            setTimeout(checkDataLoad, 2000); // 初始等待2秒
        });
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    formatDateTime(date) {
        return date.toISOString().slice(0, 19).replace('T', ' ');
    }

    getDateFormatByTimeType(timeType) {
        // 根据时间类型返回对应的日期格式
        const formats = {
            '-1': 'yyyy-MM-dd HH:mm:ss',  // 实时
            '0': 'yyyy-MM-dd HH:mm:ss',   // 分钟
            '1': 'yyyy-MM-dd HH:00:00',   // 小时
            '2': 'yyyy-MM-dd 00:00:00',   // 日
            '3': 'yyyy-MM-01 00:00:00',   // 月
            '4': 'yyyy-01-01 00:00:00'    // 年
        };
        return formats[timeType] || formats['1'];
    }

    formatDateByType(date, format) {
        // 格式化日期
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return format
            .replace('yyyy', year)
            .replace('MM', month)
            .replace('dd', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    stopAnalysis() {
        this.isAnalyzing = false;
        this.updateStatus('已停止');
        
        // 更新按钮状态
        document.getElementById('start-analysis-btn').disabled = false;
        document.getElementById('stop-analysis-btn').disabled = true;
    }

    async startLocalAnalysis() {
        // 从storage获取配置
        const result = await chrome.storage.local.get('config');
        const config = result.config || {};
        
        await this.startAnalysis(config);
    }

    exportResults() {
        if (this.analysisResults.length === 0) {
            alert('没有可导出的数据');
            return;
        }
        
        chrome.runtime.sendMessage({
            action: 'exportResults',
            format: 'csv'
        });
    }
}

// 初始化分析器
const analyzer = new MonitoringDataAnalyzer();
