// 环境监测数据自动化查询和分析 - Content Script
class MonitoringDataAnalyzer {
    constructor() {
        this.isAnalyzing = false;
        this.config = null;
        this.currentQueryIndex = 0;
        this.queryQueue = [];
        this.analysisResults = [];
        this.init();
    }

    init() {
        // 监听来自background的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true;
        });

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializePage());
        } else {
            this.initializePage();
        }
    }

    initializePage() {
        // 添加分析控制面板
        this.addAnalysisPanel();
        
        // 检测页面元素
        this.detectPageElements();
        
        console.log('环境监测数据分析插件已加载');
    }

    addAnalysisPanel() {
        // 创建浮动控制面板
        const panel = document.createElement('div');
        panel.id = 'monitoring-analysis-panel';
        panel.innerHTML = `
            <div class="panel-header">
                <span>数据分析控制</span>
                <button id="panel-toggle">−</button>
            </div>
            <div class="panel-content">
                <div class="status-info">
                    <div>状态: <span id="analysis-status">就绪</span></div>
                    <div>进度: <span id="analysis-progress">0%</span></div>
                    <div>异常: <span id="anomaly-count">0</span></div>
                </div>
                <div class="panel-actions">
                    <button id="start-analysis-btn">开始分析</button>
                    <button id="stop-analysis-btn" disabled>停止分析</button>
                    <button id="export-results-btn">导出结果</button>
                </div>
            </div>
        `;
        
        document.body.appendChild(panel);
        this.bindPanelEvents();
    }

    bindPanelEvents() {
        // 面板切换
        document.getElementById('panel-toggle').addEventListener('click', () => {
            const content = document.querySelector('.panel-content');
            const toggle = document.getElementById('panel-toggle');
            if (content.style.display === 'none') {
                content.style.display = 'block';
                toggle.textContent = '−';
            } else {
                content.style.display = 'none';
                toggle.textContent = '+';
            }
        });

        // 开始分析
        document.getElementById('start-analysis-btn').addEventListener('click', () => {
            this.startLocalAnalysis();
        });

        // 停止分析
        document.getElementById('stop-analysis-btn').addEventListener('click', () => {
            this.stopAnalysis();
        });

        // 导出结果
        document.getElementById('export-results-btn').addEventListener('click', () => {
            this.exportResults();
        });
    }

    detectPageElements() {
        // 检测页面关键元素
        this.elements = {
            outletType: document.querySelector('select[name*="排口类型"], select[name*="OutletType"]'),
            region: document.querySelector('select[name*="地区"], select[name*="Region"]'),
            company: document.querySelector('select[name*="企业"], select[name*="Company"]'),
            outlet: document.querySelector('select[name*="排口"], select[name*="Outlet"]'),
            parameter: document.querySelector('select[name*="监测项目"], select[name*="Parameter"]'),
            startTime: document.querySelector('input[name*="开始时间"], input[name*="StartTime"]'),
            endTime: document.querySelector('input[name*="结束时间"], input[name*="EndTime"]'),
            queryButton: document.querySelector('input[value*="查询"], button[onclick*="查询"]'),
            dataTable: document.querySelector('table[id*="data"], table[class*="data"]')
        };

        console.log('检测到的页面元素:', this.elements);
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'startAnalysis':
                    await this.startAnalysis(request.config);
                    sendResponse({ success: true });
                    break;

                case 'stopAnalysis':
                    this.stopAnalysis();
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Content script error:', error);
            sendResponse({ error: error.message });
        }
    }

    async startAnalysis(config) {
        if (this.isAnalyzing) {
            throw new Error('分析已在进行中');
        }

        this.config = config;
        this.isAnalyzing = true;
        this.currentQueryIndex = 0;
        this.analysisResults = [];

        this.updateStatus('正在准备查询队列...');
        
        // 生成查询队列
        await this.generateQueryQueue();
        
        this.updateStatus('开始批量查询...');
        
        // 开始批量查询
        await this.processBatchQueries();
    }

    async generateQueryQueue() {
        this.queryQueue = [];
        
        // 获取所有可选项
        const outletTypes = this.getSelectOptions(this.elements.outletType);
        const regions = this.getSelectOptions(this.elements.region);
        
        // 生成所有组合
        for (const outletType of outletTypes) {
            for (const region of regions) {
                // 设置排口类型和地区
                await this.setSelectValue(this.elements.outletType, outletType.value);
                await this.setSelectValue(this.elements.region, region.value);
                
                // 等待企业列表更新
                await this.waitForUpdate();
                
                // 获取企业列表
                const companies = this.getSelectOptions(this.elements.company);
                
                for (const company of companies) {
                    // 设置企业
                    await this.setSelectValue(this.elements.company, company.value);
                    
                    // 等待排口列表更新
                    await this.waitForUpdate();
                    
                    // 获取排口列表
                    const outlets = this.getSelectOptions(this.elements.outlet);
                    
                    for (const outlet of outlets) {
                        this.queryQueue.push({
                            outletType: outletType,
                            region: region,
                            company: company,
                            outlet: outlet
                        });
                    }
                }
            }
        }
        
        console.log(`生成查询队列，共${this.queryQueue.length}个查询任务`);
    }

    async processBatchQueries() {
        const total = this.queryQueue.length;
        
        for (let i = 0; i < total && this.isAnalyzing; i++) {
            const query = this.queryQueue[i];
            this.currentQueryIndex = i;
            
            this.updateStatus(`正在查询 ${i + 1}/${total}: ${query.company.text}`);
            this.updateProgress((i / total) * 100);
            
            try {
                await this.executeQuery(query);
                await this.analyzeCurrentData(query);
                
                // 查询间隔，避免请求过于频繁
                await this.delay(1000);
                
            } catch (error) {
                console.error(`查询失败:`, error);
                // 继续下一个查询
            }
        }
        
        if (this.isAnalyzing) {
            this.updateStatus('分析完成');
            this.updateProgress(100);
            this.isAnalyzing = false;
        }
    }

    async executeQuery(query) {
        // 设置查询条件
        await this.setSelectValue(this.elements.outletType, query.outletType.value);
        await this.setSelectValue(this.elements.region, query.region.value);
        await this.setSelectValue(this.elements.company, query.company.value);
        await this.setSelectValue(this.elements.outlet, query.outlet.value);
        
        // 设置时间范围
        if (this.config.timeRange === 'custom') {
            this.elements.startTime.value = this.config.startTime;
            this.elements.endTime.value = this.config.endTime;
        } else {
            const endTime = new Date();
            const startTime = new Date(endTime.getTime() - this.config.timeRange * 60 * 60 * 1000);
            
            this.elements.startTime.value = this.formatDateTime(startTime);
            this.elements.endTime.value = this.formatDateTime(endTime);
        }
        
        // 点击查询按钮
        this.elements.queryButton.click();
        
        // 等待数据加载
        await this.waitForDataLoad();
    }

    async analyzeCurrentData(query) {
        // 获取当前页面的数据表格
        const dataRows = this.extractTableData();
        
        if (dataRows.length === 0) {
            return;
        }
        
        // 分析数据异常
        const anomalies = this.detectAnomalies(dataRows);
        
        if (anomalies.length > 0) {
            const result = {
                timestamp: new Date().toISOString(),
                outletType: query.outletType.text,
                region: query.region.text,
                company: query.company.text,
                outlet: query.outlet.text,
                dataCount: dataRows.length,
                anomalies: anomalies
            };
            
            this.analysisResults.push(result);
            
            // 发送结果到background
            chrome.runtime.sendMessage({
                action: 'dataAnalyzed',
                data: result
            });
            
            this.updateAnomalyCount();
        }
    }

    // 工具方法
    getSelectOptions(selectElement) {
        if (!selectElement) return [];
        
        return Array.from(selectElement.options)
            .filter(option => option.value && option.value !== '')
            .map(option => ({
                value: option.value,
                text: option.text
            }));
    }

    async setSelectValue(selectElement, value) {
        if (!selectElement) return;
        
        selectElement.value = value;
        selectElement.dispatchEvent(new Event('change', { bubbles: true }));
    }

    extractTableData() {
        if (!this.elements.dataTable) return [];
        
        const rows = this.elements.dataTable.querySelectorAll('tbody tr');
        const data = [];
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > 0) {
                const rowData = Array.from(cells).map(cell => cell.textContent.trim());
                data.push(rowData);
            }
        });
        
        return data;
    }

    detectAnomalies(dataRows) {
        const anomalies = [];
        const rules = this.config.analysisRules[this.config.thresholdType];
        
        dataRows.forEach(row => {
            // 根据实际数据结构调整索引
            const parameter = row[4]; // 监测项目
            const value = parseFloat(row[5]); // 监测值
            
            if (isNaN(value)) return;
            
            // 检查各项指标
            Object.keys(rules).forEach(ruleKey => {
                if (parameter.toLowerCase().includes(ruleKey)) {
                    const rule = rules[ruleKey];
                    let anomalyType = null;
                    
                    if (rule.min !== undefined && value < rule.min) {
                        anomalyType = '低于最小值';
                    } else if (rule.max !== undefined && value > rule.max) {
                        anomalyType = '超过最大值';
                    }
                    
                    if (anomalyType) {
                        anomalies.push({
                            parameter: parameter,
                            value: value,
                            threshold: rule.min !== undefined ? `${rule.min}-${rule.max}` : `≤${rule.max}`,
                            type: anomalyType,
                            severity: value > rule.max * 2 ? '严重' : '轻微'
                        });
                    }
                }
            });
        });
        
        return anomalies;
    }

    // UI更新方法
    updateStatus(status) {
        const statusElement = document.getElementById('analysis-status');
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    updateProgress(progress) {
        const progressElement = document.getElementById('analysis-progress');
        if (progressElement) {
            progressElement.textContent = `${Math.round(progress)}%`;
        }
    }

    updateAnomalyCount() {
        const countElement = document.getElementById('anomaly-count');
        if (countElement) {
            const totalAnomalies = this.analysisResults.reduce((sum, result) => 
                sum + result.anomalies.length, 0);
            countElement.textContent = totalAnomalies;
        }
    }

    // 辅助方法
    async waitForUpdate() {
        return new Promise(resolve => setTimeout(resolve, 500));
    }

    async waitForDataLoad() {
        return new Promise(resolve => setTimeout(resolve, 2000));
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    formatDateTime(date) {
        return date.toISOString().slice(0, 19).replace('T', ' ');
    }

    stopAnalysis() {
        this.isAnalyzing = false;
        this.updateStatus('已停止');
        
        // 更新按钮状态
        document.getElementById('start-analysis-btn').disabled = false;
        document.getElementById('stop-analysis-btn').disabled = true;
    }

    async startLocalAnalysis() {
        // 从storage获取配置
        const result = await chrome.storage.local.get('config');
        const config = result.config || {};
        
        await this.startAnalysis(config);
    }

    exportResults() {
        if (this.analysisResults.length === 0) {
            alert('没有可导出的数据');
            return;
        }
        
        chrome.runtime.sendMessage({
            action: 'exportResults',
            format: 'csv'
        });
    }
}

// 初始化分析器
const analyzer = new MonitoringDataAnalyzer();
