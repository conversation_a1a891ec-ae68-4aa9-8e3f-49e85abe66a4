* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    min-height: 500px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    font-size: 14px;
    background: #f5f5f5;
}

.container {
    padding: 16px;
}

header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #4CAF50;
}

header h1 {
    font-size: 16px;
    color: #333;
    font-weight: 600;
}

.section {
    background: white;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section h3 {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #555;
    font-size: 12px;
}

.form-group input[type="checkbox"] {
    margin-right: 6px;
}

.form-group select,
.form-group input[type="datetime-local"] {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
    background: white;
}

.form-group select:focus,
.form-group input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.actions {
    display: flex;
    gap: 8px;
    margin: 16px 0;
}

.actions button {
    flex: 1;
    padding: 8px 12px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #45a049;
}

.btn-secondary {
    background: #f44336;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #da190b;
}

.btn-info {
    background: #2196F3;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: #0b7dda;
}

button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.status {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: #e0e0e0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #8BC34A);
    width: 0%;
    transition: width 0.3s ease;
}

#statusText {
    font-size: 12px;
    color: #666;
    text-align: center;
}

.stats {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.stat-label {
    color: #666;
}

.stat-item span:last-child {
    font-weight: 600;
    color: #333;
}

/* 动画效果 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.analyzing {
    animation: pulse 1.5s infinite;
}

/* 响应式调整 */
@media (max-width: 400px) {
    body {
        width: 320px;
    }
    
    .container {
        padding: 12px;
    }
    
    .actions {
        flex-direction: column;
    }
}
